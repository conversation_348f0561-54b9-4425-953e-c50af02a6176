"""
量子机器学习示例代码
包含量子分类器、量子神经网络和量子数据分析的基本实现
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_circles
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 安装所需包的命令（在终端运行）:
# pip install qiskit qiskit-machine-learning pennylane pennylane-qiskit

try:
    import qiskit
    from qiskit import QuantumCircuit, QuantumRegister, ClassicalRegister
    from qiskit.circuit.library import RealAmplitudes, ZZFeatureMap
    from qiskit_algorithms.optimizers import COBYLA
    from qiskit_machine_learning.algorithms import VQC
    from qiskit_machine_learning.neural_networks import SamplerQNN
    QISKIT_AVAILABLE = True
except ImportError:
    print("Qiskit未安装，将使用模拟实现")
    QISKIT_AVAILABLE = False

try:
    import pennylane as qml
    PENNYLANE_AVAILABLE = True
except ImportError:
    print("PennyLane未安装，将使用模拟实现")
    PENNYLANE_AVAILABLE = False

class QuantumClassifier:
    """量子分类器实现"""
    
    def __init__(self, n_qubits=2, n_layers=2):
        self.n_qubits = n_qubits
        self.n_layers = n_layers
        self.params = None
        
    def create_quantum_circuit(self, x, params):
        """创建量子电路"""
        if QISKIT_AVAILABLE:
            # 使用Qiskit实现
            qc = QuantumCircuit(self.n_qubits)
            
            # 数据编码层
            for i in range(self.n_qubits):
                qc.ry(x[i % len(x)], i)
            
            # 参数化层
            param_idx = 0
            for layer in range(self.n_layers):
                for i in range(self.n_qubits):
                    qc.ry(params[param_idx], i)
                    param_idx += 1
                for i in range(self.n_qubits - 1):
                    qc.cx(i, i + 1)
            
            return qc
        else:
            # 模拟实现
            return self._simulate_quantum_circuit(x, params)
    
    def _simulate_quantum_circuit(self, x, params):
        """模拟量子电路计算"""
        # 简化的量子计算模拟
        result = 0
        for i, xi in enumerate(x):
            for j, param in enumerate(params):
                result += np.sin(xi * param + i * j * 0.1)
        return np.tanh(result)
    
    def predict_proba(self, X, params):
        """预测概率"""
        probabilities = []
        for x in X:
            if QISKIT_AVAILABLE:
                # 实际量子计算实现会更复杂
                prob = abs(self._simulate_quantum_circuit(x, params))
            else:
                prob = abs(self._simulate_quantum_circuit(x, params))
            probabilities.append([1-prob, prob])
        return np.array(probabilities)
    
    def fit(self, X, y, max_iter=100):
        """训练量子分类器"""
        n_params = self.n_qubits * self.n_layers
        self.params = np.random.random(n_params) * 2 * np.pi
        
        def cost_function(params):
            predictions = self.predict_proba(X, params)
            loss = 0
            for i, pred in enumerate(predictions):
                target = [1-y[i], y[i]]
                loss += np.sum((pred - target)**2)
            return loss / len(X)
        
        # 简单的梯度下降优化
        learning_rate = 0.1
        for iteration in range(max_iter):
            # 数值梯度计算
            grad = np.zeros_like(self.params)
            eps = 1e-7
            
            for i in range(len(self.params)):
                params_plus = self.params.copy()
                params_minus = self.params.copy()
                params_plus[i] += eps
                params_minus[i] -= eps
                
                grad[i] = (cost_function(params_plus) - cost_function(params_minus)) / (2 * eps)
            
            self.params -= learning_rate * grad
            
            if iteration % 20 == 0:
                loss = cost_function(self.params)
                print(f"Iteration {iteration}, Loss: {loss:.4f}")
    
    def predict(self, X):
        """预测类别"""
        probas = self.predict_proba(X, self.params)
        return (probas[:, 1] > 0.5).astype(int)

class QuantumNeuralNetwork:
    """量子神经网络实现"""
    
    def __init__(self, n_qubits=4, n_layers=3):
        self.n_qubits = n_qubits
        self.n_layers = n_layers
        
        if PENNYLANE_AVAILABLE:
            self.dev = qml.device('default.qubit', wires=n_qubits)
            self.qnode = qml.QNode(self._quantum_circuit, self.dev)
        
    def _quantum_circuit(self, inputs, weights):
        """PennyLane量子电路"""
        if not PENNYLANE_AVAILABLE:
            return 0
            
        # 数据编码
        for i in range(len(inputs)):
            qml.RY(inputs[i], wires=i % self.n_qubits)
        
        # 参数化层
        for layer in range(self.n_layers):
            for i in range(self.n_qubits):
                qml.RY(weights[layer, i, 0], wires=i)
                qml.RZ(weights[layer, i, 1], wires=i)
            
            for i in range(self.n_qubits - 1):
                qml.CNOT(wires=[i, i + 1])
        
        return qml.expval(qml.PauliZ(0))
    
    def forward(self, inputs, weights):
        """前向传播"""
        if PENNYLANE_AVAILABLE:
            return self.qnode(inputs, weights)
        else:
            # 模拟实现
            result = 0
            for i, inp in enumerate(inputs):
                for layer in range(self.n_layers):
                    for qubit in range(self.n_qubits):
                        w1, w2 = weights[layer, qubit]
                        result += np.sin(inp * w1) * np.cos(inp * w2)
            return np.tanh(result)

def quantum_data_analysis_demo():
    """量子数据分析演示"""
    print("=== 量子机器学习数据分析演示 ===\n")
    
    # 1. 生成示例数据
    print("1. 生成二分类数据集...")
    X, y = make_classification(n_samples=100, n_features=2, n_redundant=0, 
                             n_informative=2, n_clusters_per_class=1, random_state=42)
    X = StandardScaler().fit_transform(X)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 2. 量子分类器
    print("\n2. 训练量子分类器...")
    qc = QuantumClassifier(n_qubits=2, n_layers=2)
    qc.fit(X_train, y_train, max_iter=50)
    
    # 预测
    y_pred = qc.predict(X_test)
    accuracy = np.mean(y_pred == y_test)
    print(f"量子分类器准确率: {accuracy:.4f}")
    
    # 3. 量子神经网络
    print("\n3. 量子神经网络演示...")
    qnn = QuantumNeuralNetwork(n_qubits=2, n_layers=2)
    
    # 随机权重
    weights = np.random.random((2, 2, 2)) * 2 * np.pi
    
    # 测试前向传播
    sample_input = X_test[0]
    output = qnn.forward(sample_input, weights)
    print(f"量子神经网络输出示例: {output:.4f}")
    
    # 4. 可视化结果
    plt.figure(figsize=(15, 5))
    
    # 原始数据
    plt.subplot(1, 3, 1)
    plt.scatter(X[y==0, 0], X[y==0, 1], c='red', alpha=0.6, label='Class 0')
    plt.scatter(X[y==1, 0], X[y==1, 1], c='blue', alpha=0.6, label='Class 1')
    plt.title('原始数据分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 量子分类结果
    plt.subplot(1, 3, 2)
    plt.scatter(X_test[y_test==0, 0], X_test[y_test==0, 1], c='red', alpha=0.6, label='True Class 0')
    plt.scatter(X_test[y_test==1, 0], X_test[y_test==1, 1], c='blue', alpha=0.6, label='True Class 1')
    plt.scatter(X_test[y_pred==0, 0], X_test[y_pred==0, 1], marker='x', c='red', s=100, label='Pred Class 0')
    plt.scatter(X_test[y_pred==1, 0], X_test[y_pred==1, 1], marker='x', c='blue', s=100, label='Pred Class 1')
    plt.title(f'量子分类结果 (准确率: {accuracy:.3f})')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 量子优势分析
    plt.subplot(1, 3, 3)
    classical_complexity = [n**2 for n in range(1, 11)]
    quantum_complexity = [2**n for n in range(1, 11)]
    
    plt.plot(range(1, 11), classical_complexity, 'r-', label='经典算法 O(n²)', linewidth=2)
    plt.plot(range(1, 11), quantum_complexity, 'b-', label='量子算法 O(2ⁿ)', linewidth=2)
    plt.xlabel('问题规模')
    plt.ylabel('计算复杂度')
    plt.title('量子vs经典计算复杂度')
    plt.legend()
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('quantum_ml_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return qc, qnn

def quantum_feature_analysis():
    """量子特征分析"""
    print("\n=== 量子特征分析 ===")
    
    # 生成复杂数据集
    X, y = make_circles(n_samples=200, noise=0.1, factor=0.3, random_state=42)
    X = StandardScaler().fit_transform(X)
    
    print("分析量子特征映射的优势...")
    
    # 模拟量子特征映射
    def quantum_feature_map(x, n_qubits=4):
        """量子特征映射"""
        features = []
        for i in range(n_qubits):
            for j in range(len(x)):
                # 模拟量子特征
                feature = np.sin(x[j] * np.pi * (i + 1)) * np.cos(x[j] * np.pi * (j + 1))
                features.append(feature)
        return np.array(features)
    
    # 应用量子特征映射
    X_quantum = np.array([quantum_feature_map(x) for x in X])
    
    print(f"原始特征维度: {X.shape[1]}")
    print(f"量子特征维度: {X_quantum.shape[1]}")
    print(f"维度扩展倍数: {X_quantum.shape[1] / X.shape[1]:.1f}x")
    
    # 可视化特征分布
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.scatter(X[y==0, 0], X[y==0, 1], c='red', alpha=0.6, label='Class 0')
    plt.scatter(X[y==1, 0], X[y==1, 1], c='blue', alpha=0.6, label='Class 1')
    plt.title('原始特征空间')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.scatter(X_quantum[y==0, 0], X_quantum[y==0, 1], c='red', alpha=0.6, label='Class 0')
    plt.scatter(X_quantum[y==1, 0], X_quantum[y==1, 1], c='blue', alpha=0.6, label='Class 1')
    plt.title('量子特征空间 (前2维)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    # 特征重要性分析
    feature_importance = np.var(X_quantum, axis=0)
    plt.bar(range(len(feature_importance)), feature_importance)
    plt.title('量子特征重要性')
    plt.xlabel('特征索引')
    plt.ylabel('方差')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('quantum_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("量子机器学习演示程序")
    print("=" * 50)
    
    # 检查依赖
    if not QISKIT_AVAILABLE and not PENNYLANE_AVAILABLE:
        print("注意: 量子计算库未安装，使用模拟实现")
        print("要获得完整功能，请安装:")
        print("pip install qiskit qiskit-machine-learning")
        print("pip install pennylane pennylane-qiskit")
        print()
    
    # 运行演示
    qc, qnn = quantum_data_analysis_demo()
    quantum_feature_analysis()
    
    print("\n=== 量子机器学习总结 ===")
    print("1. 量子并行性: 可同时处理指数级状态")
    print("2. 量子纠缠: 捕获复杂数据关联")
    print("3. 量子干涉: 优化搜索过程")
    print("4. 高维特征空间: 自然处理复杂模式")
    print("5. 潜在指数级加速: 特定问题上的优势")
