import os
import shutil
from pathlib import Path
import re

def organize_files_by_name_and_date(source_dir):
    """
    将文件按照姓名和时间进行分组整理
    文件名格式: 姓名-时间-其他信息.nii.gz
    例如: 艾俊文-20200424-801-DWI-0-50-800.nii.gz

    整理规则:
    - 如果一个姓名只有一个时间点，直接放在姓名文件夹下
    - 如果一个姓名有多个时间点，则在姓名文件夹下创建时间子文件夹

    整理后的结构:
    source_dir/
    ├── 艾俊文/  (只有一个时间点)
    │   └── 艾俊文-20200424-801-DWI-0-50-800.nii.gz
    ├── 李明/    (有多个时间点)
    │   ├── 20200424/
    │   │   └── 李明-20200424-xxx.nii.gz
    │   └── 20200425/
    │       └── 李明-20200425-xxx.nii.gz
    """

    source_path = Path(source_dir)

    if not source_path.exists():
        print(f"错误: 源目录 {source_dir} 不存在")
        return

    # 获取所有.nii.gz文件
    nii_files = list(source_path.glob("*.nii.gz"))

    if not nii_files:
        print(f"在 {source_dir} 中没有找到.nii.gz文件")
        return

    print(f"找到 {len(nii_files)} 个.nii.gz文件")

    # 第一步：分析文件，按姓名和时间分组
    file_groups = {}
    valid_files = []

    for file_path in sorted(nii_files):
        filename = file_path.name

        # 解析文件名: 姓名-时间-其他信息.nii.gz
        parts = filename.split('-')

        if len(parts) < 2:
            print(f"警告: 文件名格式不符合预期，跳过: {filename}")
            continue

        name = parts[0]  # 姓名
        date = parts[1]  # 时间

        # 验证时间格式 (应该是8位数字)
        if not re.match(r'^\d{8}$', date):
            print(f"警告: 时间格式不正确，跳过: {filename}")
            continue

        # 记录有效文件
        valid_files.append((file_path, name, date))

        # 分组统计
        if name not in file_groups:
            file_groups[name] = set()
        file_groups[name].add(date)

    # 第二步：根据时间点数量决定目录结构并移动文件
    processed_files = 0
    created_dirs = set()

    for file_path, name, date in valid_files:
        filename = file_path.name
        name_dir = source_path / name

        # 判断该姓名是否有多个时间点
        if len(file_groups[name]) > 1:
            # 多个时间点：创建时间子文件夹
            target_dir = name_dir / date
            target_path = target_dir / filename
            move_info = f"{name}/{date}/"
        else:
            # 只有一个时间点：直接放在姓名文件夹下
            target_dir = name_dir
            target_path = target_dir / filename
            move_info = f"{name}/"

        # 创建目录
        try:
            target_dir.mkdir(parents=True, exist_ok=True)
            created_dirs.add(str(target_dir))
        except Exception as e:
            print(f"错误: 无法创建目录 {target_dir}: {e}")
            continue

        # 移动文件
        try:
            if target_path.exists():
                print(f"警告: 目标文件已存在，跳过: {target_path}")
                continue

            shutil.move(str(file_path), str(target_path))
            processed_files += 1
            print(f"已移动: {filename} -> {move_info}")

        except Exception as e:
            print(f"错误: 无法移动文件 {filename}: {e}")

    print(f"\n整理完成!")
    print(f"处理文件数: {processed_files}")
    print(f"创建目录数: {len(created_dirs)}")

    # 显示创建的目录结构
    if created_dirs:
        print(f"\n创建的目录结构:")
        for dir_path in sorted(created_dirs):
            rel_path = Path(dir_path).relative_to(source_path)
            print(f"  {rel_path}")

    # 显示每个姓名的时间点统计
    print(f"\n姓名和时间点统计:")
    for name in sorted(file_groups.keys()):
        dates = sorted(file_groups[name])
        if len(dates) > 1:
            print(f"  {name}: {len(dates)}个时间点 {dates} (创建时间子文件夹)")
        else:
            print(f"  {name}: {len(dates)}个时间点 {dates} (直接放在姓名文件夹)")

def preview_organization(source_dir):
    """
    预览文件整理结果，不实际移动文件
    """
    source_path = Path(source_dir)

    if not source_path.exists():
        print(f"错误: 源目录 {source_dir} 不存在")
        return

    nii_files = list(source_path.glob("*.nii.gz"))

    if not nii_files:
        print(f"在 {source_dir} 中没有找到.nii.gz文件")
        return

    print(f"预览整理结果 (共 {len(nii_files)} 个文件):")
    print("=" * 50)

    # 分析文件，按姓名和时间分组
    file_groups = {}
    organization = {}

    for file_path in sorted(nii_files):
        filename = file_path.name
        parts = filename.split('-')

        if len(parts) < 2:
            print(f"格式错误: {filename}")
            continue

        name = parts[0]
        date = parts[1]

        if not re.match(r'^\d{8}$', date):
            print(f"时间格式错误: {filename}")
            continue

        # 统计每个姓名的时间点
        if name not in file_groups:
            file_groups[name] = set()
        file_groups[name].add(date)

        # 组织文件结构
        if name not in organization:
            organization[name] = {}

        if date not in organization[name]:
            organization[name][date] = []

        organization[name][date].append(filename)

    # 显示预览结果
    for name in sorted(organization.keys()):
        dates = sorted(organization[name].keys())

        if len(file_groups[name]) > 1:
            # 多个时间点：显示时间子文件夹结构
            print(f"\n{name}/ (多个时间点，创建时间子文件夹)")
            for date in dates:
                print(f"  {date}/")
                for filename in sorted(organization[name][date]):
                    print(f"    {filename}")
        else:
            # 单个时间点：直接放在姓名文件夹下
            print(f"\n{name}/ (单个时间点，直接放在姓名文件夹)")
            for date in dates:
                for filename in sorted(organization[name][date]):
                    print(f"  {filename}")

    # 显示统计信息
    print(f"\n统计信息:")
    print(f"总姓名数: {len(organization)}")
    single_date_count = sum(1 for name in file_groups if len(file_groups[name]) == 1)
    multi_date_count = sum(1 for name in file_groups if len(file_groups[name]) > 1)
    print(f"单时间点姓名: {single_date_count}")
    print(f"多时间点姓名: {multi_date_count}")

def main():
    # 设置源目录路径
    source_directory = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\ruijin备用"
    
    print("文件整理工具")
    print("=" * 50)
    print(f"源目录: {source_directory}")
    print()
    
    # 先预览整理结果
    print("1. 预览整理结果:")
    preview_organization(source_directory)
    
    print("\n" + "=" * 50)
    
    # 询问是否执行实际整理
    while True:
        choice = input("\n是否执行实际的文件整理? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("\n2. 执行文件整理:")
            organize_files_by_name_and_date(source_directory)
            break
        elif choice in ['n', 'no', '否']:
            print("取消文件整理操作")
            break
        else:
            print("请输入 y 或 n")

if __name__ == "__main__":
    main()
