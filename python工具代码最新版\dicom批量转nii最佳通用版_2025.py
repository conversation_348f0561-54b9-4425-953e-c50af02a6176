#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM批量转NII.GZ通用版 - 2025年版本
支持多种目录结构的DICOM文件批量转换为NII.GZ格式
文件名格式：文件夹名-序列名.nii.gz

作者：AI Assistant
日期：2025年
""" 
# conda install -c conda-forge pydicom
# pip install dicom2nifti

import os
import sys
import logging
import traceback
from pathlib import Path
from typing import List, Tuple, Optional
import dicom2nifti
import dicom2nifti.settings as settings
import pydicom
import numpy as np

# 禁用所有验证以提高兼容性
settings.disable_validate_slice_increment()
settings.disable_validate_orientation()
settings.disable_validate_multiframe_implicit()
settings.disable_validate_orthogonal()
settings.disable_validate_slicecount()

class DicomToNiftiConverter:
    """DICOM到NIfTI转换器"""
    
    def __init__(self, base_path: str, output_path: Optional[str] = None):
        """
        初始化转换器
        
        Args:
            base_path: DICOM文件的基础路径
            output_path: 输出路径，如果为None则在原路径下创建nii文件夹
        """
        self.base_path = Path(base_path)
        self.output_path = Path(output_path) if output_path else self.base_path / "nii_output"
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def find_dicom_files(self, directory: Path) -> List[Path]:
        """查找目录中的DICOM文件"""
        dicom_extensions = ['.dcm', '.DCM', '.dicom', '.DICOM']
        dicom_files = []

        for ext in dicom_extensions:
            dicom_files.extend(directory.glob(f'*{ext}'))

        # 如果没有找到带扩展名的文件，尝试检查无扩展名的文件
        if not dicom_files:
            for file_path in directory.iterdir():
                if file_path.is_file() and file_path.name not in ['StudyInfo.dat', 'DICOMDIR', 'VERSION', 'Thumbs.db']:
                    try:
                        # 检查文件大小，太小的文件跳过
                        if file_path.stat().st_size < 1024:
                            continue

                        ds = pydicom.dcmread(str(file_path), force=True)
                        # 检查是否有像素数据
                        if hasattr(ds, 'pixel_array') or hasattr(ds, 'PixelData'):
                            dicom_files.append(file_path)
                    except:
                        continue

        return dicom_files
    
    def get_sequence_info(self, dicom_file: Path) -> Tuple[str, str]:
        """
        从DICOM文件中获取序列信息
        
        Returns:
            Tuple[序列号, 序列描述]
        """
        try:
            ds = pydicom.dcmread(str(dicom_file), force=True)
            series_number = str(getattr(ds, 'SeriesNumber', 'Unknown'))
            series_description = getattr(ds, 'SeriesDescription', 'Unknown').strip()
            
            # 清理序列描述中的非法字符
            series_description = "".join(c if c.isalnum() or c in ('-', '_', ' ') else '_' for c in series_description)
            series_description = series_description.replace(' ', '_')
            
            return series_number, series_description
        except Exception as e:
            self.logger.warning(f"无法读取DICOM文件 {dicom_file}: {e}")
            return "Unknown", "Unknown"

    def _check_existing_file(self, output_file: Path, patient_name: str = None, sequence_id: str = None) -> bool:
        """
        检查输出文件是否已存在且有效

        Args:
            output_file: 输出文件路径
            patient_name: 患者名称
            sequence_id: 序列标识符

        Returns:
            文件是否已存在且有效
        """
        if not output_file.exists():
            return False

        try:
            # 检查文件大小（至少应该大于1KB）
            file_size = output_file.stat().st_size
            if file_size < 1024:
                self.logger.warning(f"文件 {output_file.name} 太小 ({file_size} bytes)，可能损坏")
                return False

            # 尝试使用nibabel验证NIfTI文件
            try:
                import nibabel as nib
                img = nib.load(str(output_file))
                # 检查图像是否有有效的形状
                if len(img.shape) < 3:
                    self.logger.warning(f"文件 {output_file.name} 维度不足 ({img.shape})")
                    return False

                self.logger.info(f"✅ 跳过已存在的有效文件: {output_file.name} (大小: {file_size/1024/1024:.1f}MB, 形状: {img.shape})")
                return True

            except ImportError:
                # 如果没有nibabel，只检查文件大小
                self.logger.info(f"✅ 跳过已存在的文件: {output_file.name} (大小: {file_size/1024/1024:.1f}MB)")
                return True

        except Exception as e:
            self.logger.warning(f"检查文件 {output_file.name} 时出错: {e}")
            return False

    def detect_directory_structure(self) -> List[Tuple[Path, str, str]]:
        """
        检测目录结构并返回需要转换的序列文件夹列表
        
        Returns:
            List[Tuple[序列文件夹路径, 患者名称, 序列标识符]]
        """
        conversion_list = []
        
        self.logger.info(f"开始分析目录结构: {self.base_path}")
        
        # 遍历基础目录
        for item in self.base_path.iterdir():
            if not item.is_dir():
                continue
                
            patient_name = item.name
            self.logger.info(f"处理患者文件夹: {patient_name}")
            
            # 检查是否直接包含DICOM文件（无序列文件夹结构）
            dicom_files = self.find_dicom_files(item)
            if dicom_files:
                self.logger.info(f"  发现直接DICOM文件结构")
                conversion_list.append((item, patient_name, "dicom"))
                continue
            
            # 检查子文件夹
            for sub_item in item.iterdir():
                if not sub_item.is_dir():
                    continue
                    
                # 检查是否为序列文件夹（包含DICOM文件）
                dicom_files = self.find_dicom_files(sub_item)
                if dicom_files:
                    self.logger.info(f"  发现序列文件夹: {sub_item.name}")
                    conversion_list.append((sub_item, patient_name, sub_item.name))
                else:
                    # 可能是时间文件夹，继续向下查找
                    for sub_sub_item in sub_item.iterdir():
                        if not sub_sub_item.is_dir():
                            continue
                            
                        dicom_files = self.find_dicom_files(sub_sub_item)
                        if dicom_files:
                            self.logger.info(f"  发现深层序列文件夹: {sub_item.name}/{sub_sub_item.name}")
                            sequence_id = f"{sub_item.name}_{sub_sub_item.name}"
                            conversion_list.append((sub_sub_item, patient_name, sequence_id))
        
        self.logger.info(f"总共发现 {len(conversion_list)} 个需要转换的序列")
        return conversion_list
    
    def convert_sequence(self, sequence_path: Path, patient_name: str, sequence_id: str) -> bool:
        """
        转换单个序列，包含错误处理和重试机制

        Args:
            sequence_path: 序列文件夹路径
            patient_name: 患者名称
            sequence_id: 序列标识符

        Returns:
            转换是否成功
        """
        try:
            # 确保输出目录存在
            self.output_path.mkdir(parents=True, exist_ok=True)

            # 获取序列信息
            dicom_files = self.find_dicom_files(sequence_path)
            if not dicom_files:
                self.logger.warning(f"在 {sequence_path} 中未找到DICOM文件")
                return False

            series_number, series_description = self.get_sequence_info(dicom_files[0])

            # 生成输出文件名：患者名-序列标识符.nii.gz 或 患者名-序列号_序列描述.nii.gz
            if sequence_id == "dicom":
                output_filename = f"{patient_name}-{series_number}_{series_description}.nii.gz"
            else:
                output_filename = f"{patient_name}-{sequence_id}.nii.gz"

            output_file = self.output_path / output_filename

            # 检查文件是否已存在且有效
            if self._check_existing_file(output_file):
                return True

            # 如果文件已存在但无效，添加后缀重新生成
            counter = 1
            while output_file.exists():
                name_part = output_filename.replace('.nii.gz', '')
                output_file = self.output_path / f"{name_part}_{counter}.nii.gz"
                counter += 1

            self.logger.info(f"开始转换: {sequence_path} -> {output_file}")

            # 多种转换方法尝试
            conversion_methods = [
                self._try_standard_conversion,
                self._try_robust_conversion,
                self._try_size_filtered_conversion,
                self._try_single_timepoint_conversion
            ]

            for i, method in enumerate(conversion_methods, 1):
                try:
                    self.logger.info(f"尝试转换方法 {i}/{len(conversion_methods)}")
                    if method(sequence_path, output_file):
                        self.logger.info(f"转换成功: {output_file}")
                        return True
                except Exception as e:
                    self.logger.warning(f"转换方法 {i} 失败: {e}")
                    continue

            self.logger.error(f"所有转换方法都失败: {sequence_path}")
            return False

        except Exception as e:
            self.logger.error(f"转换失败 {sequence_path}: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _try_standard_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """标准转换方法"""
        dicom2nifti.dicom_series_to_nifti(
            str(sequence_path),
            str(output_file),
            reorient_nifti=False
        )
        return True

    def _try_robust_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """强制转换方法，忽略方向和切片验证"""
        try:
            # 禁用所有验证
            settings.disable_validate_slice_increment()
            settings.disable_validate_orientation()
            settings.disable_validate_multiframe_implicit()
            settings.disable_validate_orthogonal()
            settings.disable_validate_slicecount()

            # 尝试使用不同的参数
            dicom2nifti.dicom_series_to_nifti(
                str(sequence_path),
                str(output_file),
                reorient_nifti=False
            )
            return True

        except Exception as e:
            error_str = str(e)
            self.logger.warning(f"强制转换失败: {error_str}")

            # 如果是方向问题，尝试忽略方向
            if "IMAGE_ORIENTATION_INCONSISTENT" in error_str:
                try:
                    # 尝试使用通用转换器
                    import dicom2nifti.convert_generic as convert_generic
                    convert_generic.dicom_series_to_nifti(
                        str(sequence_path),
                        str(output_file),
                        reorient_nifti=False
                    )
                    return True
                except:
                    pass

            return False

    def _try_size_filtered_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """尺寸过滤转换方法"""
        return self._handle_size_mismatch(sequence_path, output_file)

    def _try_single_timepoint_conversion(self, sequence_path: Path, output_file: Path) -> bool:
        """单时间点转换方法"""
        return self._handle_missing_files(sequence_path, output_file)

    def _handle_missing_files(self, sequence_path: Path, output_file: Path) -> bool:
        """处理DICOM文件缺失的情况"""
        try:
            import pydicom
            dicom_files = self.find_dicom_files(sequence_path)

            if not dicom_files:
                return False

            # 多种分组策略
            grouping_strategies = [
                self._group_by_temporal_position,
                self._group_by_acquisition_time,
                self._group_by_series_time,
                self._group_by_echo_number
            ]

            best_files = None
            best_strategy = None

            for strategy in grouping_strategies:
                try:
                    groups = strategy(dicom_files)
                    if groups:
                        # 选择文件数量最多的组
                        best_group_key = max(groups.keys(), key=lambda x: len(groups[x]))
                        candidate_files = groups[best_group_key]

                        # 如果这个组的文件数量更多，或者是第一个有效的组
                        if best_files is None or len(candidate_files) > len(best_files):
                            best_files = candidate_files
                            best_strategy = strategy.__name__

                except Exception as e:
                    self.logger.warning(f"分组策略 {strategy.__name__} 失败: {e}")
                    continue

            if best_files and len(best_files) >= 1:  # 降低到至少1个文件
                self.logger.info(f"使用策略 {best_strategy}，包含 {len(best_files)} 个文件")
                return self._convert_filtered_files(best_files, output_file, sequence_path)
            else:
                self.logger.warning(f"所有分组策略都无法找到足够的文件 (最多: {len(best_files) if best_files else 0})")
                return False

        except Exception as e:
            self.logger.error(f"处理缺失文件失败: {e}")
            return False

    def _group_by_temporal_position(self, dicom_files: List[Path]) -> dict:
        """按时间位置分组"""
        groups = {}
        for dcm_file in dicom_files:
            try:
                ds = pydicom.dcmread(str(dcm_file), force=True)
                time_point = getattr(ds, 'TemporalPositionIdentifier', '0')
                if time_point not in groups:
                    groups[time_point] = []
                groups[time_point].append(dcm_file)
            except:
                continue
        return groups

    def _group_by_acquisition_time(self, dicom_files: List[Path]) -> dict:
        """按采集时间分组"""
        groups = {}
        for dcm_file in dicom_files:
            try:
                ds = pydicom.dcmread(str(dcm_file), force=True)
                acq_time = getattr(ds, 'AcquisitionTime', '000000')
                # 按小时分组
                time_group = acq_time[:2] if len(acq_time) >= 2 else '00'
                if time_group not in groups:
                    groups[time_group] = []
                groups[time_group].append(dcm_file)
            except:
                continue
        return groups

    def _group_by_series_time(self, dicom_files: List[Path]) -> dict:
        """按序列时间分组"""
        groups = {}
        for dcm_file in dicom_files:
            try:
                ds = pydicom.dcmread(str(dcm_file), force=True)
                series_time = getattr(ds, 'SeriesTime', '000000')
                time_group = series_time[:4] if len(series_time) >= 4 else '0000'
                if time_group not in groups:
                    groups[time_group] = []
                groups[time_group].append(dcm_file)
            except:
                continue
        return groups

    def _group_by_echo_number(self, dicom_files: List[Path]) -> dict:
        """按回波号分组"""
        groups = {}
        for dcm_file in dicom_files:
            try:
                ds = pydicom.dcmread(str(dcm_file), force=True)
                echo_number = getattr(ds, 'EchoNumber', 1)
                if echo_number not in groups:
                    groups[echo_number] = []
                groups[echo_number].append(dcm_file)
            except:
                continue
        return groups

    def _handle_size_mismatch(self, sequence_path: Path, output_file: Path) -> bool:
        """处理图像尺寸不匹配的情况"""
        try:
            import pydicom
            import shutil
            dicom_files = self.find_dicom_files(sequence_path)

            if not dicom_files:
                self.logger.error(f"未找到DICOM文件: {sequence_path}")
                return False

            # 分析图像尺寸和其他属性
            file_groups = {}
            valid_files_count = 0

            for dcm_file in dicom_files:
                try:
                    ds = pydicom.dcmread(str(dcm_file), force=True)
                    rows = getattr(ds, 'Rows', 0)
                    cols = getattr(ds, 'Columns', 0)

                    # 跳过无效尺寸的文件和非图像文件
                    if rows <= 0 or cols <= 0:
                        self.logger.warning(f"跳过无效尺寸文件 {dcm_file.name}: {rows}x{cols}")
                        continue

                    # 检查是否为图像数据
                    if not (hasattr(ds, 'pixel_array') or hasattr(ds, 'PixelData')):
                        self.logger.warning(f"跳过非图像文件 {dcm_file.name}")
                        continue

                    # 尝试读取像素数据以验证文件有效性
                    try:
                        pixel_data = ds.pixel_array
                        if pixel_data.size == 0:
                            self.logger.warning(f"跳过空像素数据文件 {dcm_file.name}")
                            continue
                    except Exception as pixel_error:
                        self.logger.warning(f"无法读取像素数据 {dcm_file.name}: {pixel_error}")
                        continue

                    # 获取更多属性用于分组
                    series_number = getattr(ds, 'SeriesNumber', 0)
                    slice_thickness = getattr(ds, 'SliceThickness', 0)
                    pixel_spacing = getattr(ds, 'PixelSpacing', [1, 1])

                    # 创建分组键
                    group_key = f"{rows}x{cols}_s{series_number}_t{slice_thickness}"
                    valid_files_count += 1

                    if group_key not in file_groups:
                        file_groups[group_key] = {
                            'files': [],
                            'size': f"{rows}x{cols}",
                            'series': series_number,
                            'thickness': slice_thickness,
                            'spacing': pixel_spacing
                        }
                    file_groups[group_key]['files'].append(dcm_file)

                except Exception as e:
                    self.logger.warning(f"无法读取DICOM文件 {dcm_file.name}: {e}")
                    continue

            # 检查是否有有效的文件
            if not file_groups or valid_files_count == 0:
                self.logger.error(f"未找到有效的DICOM文件: {sequence_path}")
                return False

            # 输出分组分析结果
            self.logger.info(f"文件分组分析结果:")
            for group_key, group_info in file_groups.items():
                self.logger.info(f"  {group_key}: {len(group_info['files'])} 个文件")

            # 选择文件数量最多的组
            best_group_key = max(file_groups.keys(), key=lambda x: len(file_groups[x]['files']))
            best_group = file_groups[best_group_key]
            best_files = best_group['files']

            self.logger.info(f"选择组 {best_group_key}，包含 {len(best_files)} 个文件")

            # 如果文件数量太少，尝试合并相似的组
            if len(best_files) < 3:  # 降低最小文件数量要求
                self.logger.info("文件数量较少，尝试合并相似组...")
                similar_files = []
                target_size = best_group['size']

                for group_key, group_info in file_groups.items():
                    if group_info['size'] == target_size:
                        similar_files.extend(group_info['files'])

                if len(similar_files) > len(best_files):
                    best_files = similar_files
                    self.logger.info(f"合并后包含 {len(best_files)} 个文件")

                # 如果还是太少，尝试使用所有有效文件
                if len(best_files) < 3:
                    self.logger.info("尝试使用所有有效文件...")
                    all_files = []
                    for group_info in file_groups.values():
                        all_files.extend(group_info['files'])

                    if len(all_files) >= len(best_files):
                        best_files = all_files
                        self.logger.info(f"使用所有文件，共 {len(best_files)} 个")

            return self._convert_filtered_files(best_files, output_file, sequence_path)

        except Exception as e:
            self.logger.error(f"处理尺寸不匹配失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _convert_filtered_files(self, dicom_files: List[Path], output_file: Path, sequence_path: Path) -> bool:
        """转换过滤后的DICOM文件"""
        import shutil
        temp_dir = sequence_path.parent / f"temp_{sequence_path.name}_{os.getpid()}"

        try:
            temp_dir.mkdir(exist_ok=True)

            # 按切片位置排序并复制文件
            sorted_files = []
            for dcm_file in dicom_files:
                try:
                    ds = pydicom.dcmread(str(dcm_file), force=True)
                    slice_location = getattr(ds, 'SliceLocation', 0)
                    instance_number = getattr(ds, 'InstanceNumber', 0)
                    acquisition_number = getattr(ds, 'AcquisitionNumber', 0)
                    sorted_files.append((slice_location, instance_number, acquisition_number, dcm_file))
                except:
                    # 如果无法读取位置信息，使用文件名排序
                    sorted_files.append((0, 0, 0, dcm_file))

            # 多级排序：切片位置 -> 实例号 -> 采集号
            sorted_files.sort(key=lambda x: (x[0], x[1], x[2]))

            # 复制文件到临时目录
            for i, (_, _, _, dcm_file) in enumerate(sorted_files):
                temp_file = temp_dir / f"{i:04d}.dcm"
                shutil.copy2(dcm_file, temp_file)

            self.logger.info(f"复制了 {len(sorted_files)} 个文件到临时目录")

            # 尝试转换临时目录
            dicom2nifti.dicom_series_to_nifti(
                str(temp_dir),
                str(output_file),
                reorient_nifti=False
            )

            self.logger.info(f"过滤文件转换成功: {output_file}")
            return True

        except Exception as convert_error:
            self.logger.error(f"转换过程中出错: {convert_error}")
            return False

        finally:
            # 清理临时目录
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def run_conversion(self):
        """执行批量转换"""
        self.logger.info("="*60)
        self.logger.info("开始DICOM批量转换")
        self.logger.info(f"输入路径: {self.base_path}")
        self.logger.info(f"输出路径: {self.output_path}")
        self.logger.info("="*60)

        # 检测目录结构
        conversion_list = self.detect_directory_structure()

        if not conversion_list:
            self.logger.warning("未找到需要转换的DICOM序列")
            return

        # 执行转换
        success_count = 0
        skipped_count = 0
        failed_count = 0
        total_count = len(conversion_list)

        # 记录失败的序列
        failed_sequences = []
        no_dicom_sequences = []

        for i, (sequence_path, patient_name, sequence_id) in enumerate(conversion_list, 1):
            self.logger.info(f"\n进度: {i}/{total_count}")

            # 检查是否已存在有效文件
            if sequence_id == "dicom":
                dicom_files = self.find_dicom_files(sequence_path)
                if not dicom_files:
                    self.logger.warning(f"序列 {sequence_path} 中未找到DICOM文件，跳过")
                    no_dicom_sequences.append({
                        'patient_name': patient_name,
                        'sequence_id': sequence_id,
                        'sequence_path': str(sequence_path),
                        'reason': '未找到DICOM文件'
                    })
                    continue
                series_number, series_description = self.get_sequence_info(dicom_files[0])
                output_filename = f"{patient_name}-{series_number}_{series_description}.nii.gz"
            else:
                output_filename = f"{patient_name}-{sequence_id}.nii.gz"

            output_file = self.output_path / output_filename

            if self._check_existing_file(output_file):
                skipped_count += 1
                success_count += 1  # 跳过的文件也算作成功
                continue

            if self.convert_sequence(sequence_path, patient_name, sequence_id):
                success_count += 1
            else:
                failed_count += 1
                failed_sequences.append({
                    'patient_name': patient_name,
                    'sequence_id': sequence_id,
                    'sequence_path': str(sequence_path),
                    'output_filename': output_filename
                })

        # 输出统计信息
        self.logger.info("="*60)
        self.logger.info("转换完成!")
        self.logger.info(f"总序列数: {total_count}")
        self.logger.info(f"成功转换: {success_count - skipped_count}")
        self.logger.info(f"跳过已存在: {skipped_count}")
        self.logger.info(f"总成功数: {success_count}")
        self.logger.info(f"失败数量: {failed_count}")
        self.logger.info(f"无DICOM文件: {len(no_dicom_sequences)}")
        self.logger.info(f"成功率: {success_count/(total_count - len(no_dicom_sequences))*100:.1f}%" if (total_count - len(no_dicom_sequences)) > 0 else "成功率: 0.0%")

        # 打印失败的序列详情
        if failed_sequences:
            self.logger.info("\n" + "="*60)
            self.logger.info("❌ 转换失败的序列:")
            self.logger.info("="*60)
            for i, failed in enumerate(failed_sequences, 1):
                self.logger.info(f"{i}. 患者: {failed['patient_name']}")
                self.logger.info(f"   序列: {failed['sequence_id']}")
                self.logger.info(f"   路径: {failed['sequence_path']}")
                self.logger.info(f"   目标文件: {failed['output_filename']}")
                self.logger.info("")

        # 打印无DICOM文件的序列
        if no_dicom_sequences:
            self.logger.info("\n" + "="*60)
            self.logger.info("⚠️  无DICOM文件的序列:")
            self.logger.info("="*60)
            for i, no_dicom in enumerate(no_dicom_sequences, 1):
                self.logger.info(f"{i}. 患者: {no_dicom['patient_name']}")
                self.logger.info(f"   序列: {no_dicom['sequence_id']}")
                self.logger.info(f"   路径: {no_dicom['sequence_path']}")
                self.logger.info(f"   原因: {no_dicom['reason']}")
                self.logger.info("")

        # 总结
        if failed_sequences or no_dicom_sequences:
            self.logger.info("="*60)
            self.logger.info("💡 建议:")
            if failed_sequences:
                self.logger.info("- 检查失败序列的DICOM文件是否完整")
                self.logger.info("- 查看详细错误日志了解失败原因")
                self.logger.info("- 尝试使用图像尺寸问题诊断修复工具")
            if no_dicom_sequences:
                self.logger.info("- 确认无DICOM文件的路径是否正确")
                self.logger.info("- 检查文件是否被移动或删除")

        self.logger.info("="*60)


def main():
    """主函数"""
    # 配置路径 - 请根据实际情况修改
    base_path = r"K:\肝脏MRI数据集\需要补充的T1-T2-DWI-ADC患者"
    output_path = None  # 如果为None，将在base_path下创建nii_output文件夹
    
    # 创建转换器并执行转换
    converter = DicomToNiftiConverter(base_path, output_path)
    converter.run_conversion()


if __name__ == "__main__":
    main()
