import os
import shutil
from pathlib import Path

def find_images_without_masks():
    """
    找出没有对应mask的图像文件，并将它们复制到新的文件夹中
    """
    # 定义路径
    image_folder = r"M:\肝脏MRI数据集\HCC-EOBMRI\595HCC\image\t1"
    mask_folder = r"M:\肝脏MRI数据集\HCC-EOBMRI\595HCC\mask\t1" 
    output_folder = r"M:\肝脏MRI数据集\HCC-EOBMRI\595HCC\images_without_masks\t1"
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取所有图像文件
    image_files = []
    if os.path.exists(image_folder):
        for file in os.listdir(image_folder):
            if file.endswith('.nii') or file.endswith('.nii.gz'):
                image_files.append(file)
    else:
        print(f"图像文件夹不存在: {image_folder}")
        return
    
    # 获取所有mask文件
    mask_files = []
    if os.path.exists(mask_folder):
        for file in os.listdir(mask_folder):
            if file.endswith('.nii') or file.endswith('.nii.gz'):
                mask_files.append(file)
    else:
        print(f"Mask文件夹不存在: {mask_folder}")
        return
    
    # 创建mask文件名的集合（去掉-mask后缀）
    mask_basenames = set()
    for mask_file in mask_files:
        # 处理不同的mask命名模式
        if mask_file.endswith('-t2-mask.nii'):
            # T2序列：患者名-t2-mask.nii -> 患者名-t2.nii 和 患者名-T2.nii
            basename1 = mask_file.replace('-t2-mask.nii', '-t2.nii')
            basename2 = mask_file.replace('-t2-mask.nii', '-T2.nii')
            mask_basenames.add(basename1)
            mask_basenames.add(basename2)
        elif mask_file.endswith('-t2-mask.nii.gz'):
            # T2序列：患者名-t2-mask.nii.gz -> 患者名-t2.nii.gz 和 患者名-T2.nii.gz
            basename1 = mask_file.replace('-t2-mask.nii.gz', '-t2.nii.gz')
            basename2 = mask_file.replace('-t2-mask.nii.gz', '-T2.nii.gz')
            mask_basenames.add(basename1)
            mask_basenames.add(basename2)
        elif mask_file.endswith('-t1-mask.nii'):
            # T1序列：患者名-t1-mask.nii -> 患者名-t1.nii 和 患者名-T1.nii
            basename1 = mask_file.replace('-t1-mask.nii', '-t1.nii')
            basename2 = mask_file.replace('-t1-mask.nii', '-T1.nii')
            mask_basenames.add(basename1)
            mask_basenames.add(basename2)
        elif mask_file.endswith('-t1-mask.nii.gz'):
            # T1序列：患者名-t1-mask.nii.gz -> 患者名-t1.nii.gz 和 患者名-T1.nii.gz
            basename1 = mask_file.replace('-t1-mask.nii.gz', '-t1.nii.gz')
            basename2 = mask_file.replace('-t1-mask.nii.gz', '-T1.nii.gz')
            mask_basenames.add(basename1)
            mask_basenames.add(basename2)
        elif '-mask.nii' in mask_file:
            basename = mask_file.replace('-mask.nii', '.nii')
            mask_basenames.add(basename)
        elif '-mask.nii.gz' in mask_file:
            basename = mask_file.replace('-mask.nii.gz', '.nii.gz')
            mask_basenames.add(basename)
        else:
            # 如果没有明确的mask后缀，尝试其他模式
            basename = mask_file
            mask_basenames.add(basename)
    
    print(f"找到 {len(image_files)} 个图像文件")
    print(f"找到 {len(mask_files)} 个mask文件")
    print(f"处理后的mask基础名称: {len(mask_basenames)} 个")

    # 显示前几个文件作为调试信息
    print(f"\n前5个图像文件:")
    for i, img in enumerate(sorted(image_files)[:5]):
        print(f"  {i+1}. {img}")

    print(f"\n前5个mask文件:")
    for i, mask in enumerate(sorted(mask_files)[:5]):
        print(f"  {i+1}. {mask}")

    print(f"\n前5个处理后的mask基础名称:")
    for i, basename in enumerate(sorted(mask_basenames)[:5]):
        print(f"  {i+1}. {basename}")
    
    # 找出没有对应mask的图像
    images_without_masks = []
    for image_file in image_files:
        # 检查是否有对应的mask
        has_mask = False
        
        # 直接匹配
        if image_file in mask_basenames:
            has_mask = True
        
        # 尝试不同的命名模式匹配
        if not has_mask:
            # 尝试T1大小写转换
            if image_file.endswith('-T1.nii'):
                alt_name = image_file.replace('-T1.nii', '-t1.nii')
                if alt_name in mask_basenames:
                    has_mask = True
            elif image_file.endswith('-T1.nii.gz'):
                alt_name = image_file.replace('-T1.nii.gz', '-t1.nii.gz')
                if alt_name in mask_basenames:
                    has_mask = True
            elif image_file.endswith('-t1.nii'):
                alt_name = image_file.replace('-t1.nii', '-T1.nii')
                if alt_name in mask_basenames:
                    has_mask = True
            elif image_file.endswith('-t1.nii.gz'):
                alt_name = image_file.replace('-t1.nii.gz', '-T1.nii.gz')
                if alt_name in mask_basenames:
                    has_mask = True
        
        if not has_mask:
            images_without_masks.append(image_file)
    
    print(f"\n找到 {len(images_without_masks)} 个没有对应mask的图像文件:")
    
    # 复制没有mask的图像到新文件夹
    copied_count = 0
    for image_file in images_without_masks:
        src_path = os.path.join(image_folder, image_file)
        dst_path = os.path.join(output_folder, image_file)
        
        try:
            shutil.copy2(src_path, dst_path)
            print(f"已复制: {image_file}")
            copied_count += 1
        except Exception as e:
            print(f"复制失败 {image_file}: {e}")
    
    print(f"\n总结:")
    print(f"- 图像文件总数: {len(image_files)}")
    print(f"- Mask文件总数: {len(mask_files)}")
    print(f"- 没有mask的图像: {len(images_without_masks)}")
    print(f"- 成功复制: {copied_count}")
    print(f"- 输出文件夹: {output_folder}")
    
    # 显示详细的没有mask的文件列表
    if images_without_masks:
        print(f"\n没有mask的图像文件列表:")
        for i, img in enumerate(images_without_masks, 1):
            print(f"{i:2d}. {img}")

def main():
    """主函数"""
    print("开始查找没有对应mask的图像文件...")
    find_images_without_masks()
    print("\n处理完成!")

if __name__ == "__main__":
    main()
