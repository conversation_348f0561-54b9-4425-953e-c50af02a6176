import os
import pandas as pd
from pathlib import Path
import re

def scan_patient_folders(base_dir):
    """
    扫描患者文件夹，收集所有文件信息
    
    Args:
        base_dir: 基础目录路径 (K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output)
    
    Returns:
        list: 包含文件信息的列表，每个元素为字典
    """
    base_path = Path(base_dir)
    
    if not base_path.exists():
        print(f"错误: 目录 {base_dir} 不存在")
        return []
    
    file_info_list = []
    
    # 遍历所有患者文件夹
    for patient_folder in base_path.iterdir():
        if not patient_folder.is_dir():
            continue
            
        patient_name = patient_folder.name
        print(f"正在扫描患者文件夹: {patient_name}")
        
        # 扫描患者文件夹内的内容
        scan_folder_contents(patient_folder, patient_name, "", file_info_list)
    
    return file_info_list

def scan_folder_contents(folder_path, patient_name, subfolder_path, file_info_list):
    """
    递归扫描文件夹内容
    
    Args:
        folder_path: 当前文件夹路径
        patient_name: 患者名称
        subfolder_path: 子文件夹路径（相对于患者文件夹）
        file_info_list: 文件信息列表
    """
    try:
        for item in folder_path.iterdir():
            if item.is_file():
                # 处理文件
                file_info = {
                    '患者名称': patient_name,
                    '子文件夹路径': subfolder_path,
                    '文件名': item.name,
                    '文件扩展名': item.suffix,
                    '完整路径': str(item),
                    '相对路径': str(item.relative_to(folder_path.parent)),
                    '文件大小(MB)': round(item.stat().st_size / (1024 * 1024), 2) if item.exists() else 0
                }
                
                # 如果是nii.gz文件，尝试解析更多信息
                if item.name.endswith('.nii.gz'):
                    parse_nii_filename(item.name, file_info)
                
                file_info_list.append(file_info)
                
            elif item.is_dir():
                # 处理子文件夹
                new_subfolder_path = os.path.join(subfolder_path, item.name) if subfolder_path else item.name
                scan_folder_contents(item, patient_name, new_subfolder_path, file_info_list)
                
    except PermissionError:
        print(f"警告: 无法访问文件夹 {folder_path}")
    except Exception as e:
        print(f"错误: 扫描文件夹 {folder_path} 时出现异常: {e}")

def parse_nii_filename(filename, file_info):
    """
    解析nii.gz文件名，提取更多信息

    Args:
        filename: 文件名
        file_info: 文件信息字典
    """
    # 移除.nii.gz后缀
    name_without_ext = filename.replace('.nii.gz', '')

    # 尝试按'-'分割文件名
    parts = name_without_ext.split('-')

    if len(parts) >= 2:
        file_info['解析_姓名'] = parts[0]

        # 提取第2个"-"后面的所有内容
        if len(parts) >= 3:
            # 找到第2个"-"的位置，提取后面的所有内容
            first_dash = name_without_ext.find('-')
            if first_dash != -1:
                second_dash = name_without_ext.find('-', first_dash + 1)
                if second_dash != -1:
                    file_info['第2个dash后内容'] = name_without_ext[second_dash + 1:]

        # 尝试识别时间格式 (8位数字)
        for i, part in enumerate(parts[1:], 1):
            if re.match(r'^\d{8}$', part):
                file_info['解析_时间'] = part
                file_info['解析_时间_格式化'] = f"{part[:4]}-{part[4:6]}-{part[6:8]}"
                break

        # 保存所有分割部分
        file_info['解析_所有部分'] = '-'.join(parts)
        file_info['解析_部分数量'] = len(parts)

    # 尝试识别序列类型
    filename_upper = filename.upper()
    sequence_types = ['T1', 'T2', 'DWI', 'ADC', 'FLAIR', 'T1C', 'T2W', 'T1W']
    for seq_type in sequence_types:
        if seq_type in filename_upper:
            file_info['序列类型'] = seq_type
            break

def save_to_excel(file_info_list, output_path):
    """
    将文件信息保存到Excel文件
    
    Args:
        file_info_list: 文件信息列表
        output_path: 输出Excel文件路径
    """
    if not file_info_list:
        print("没有文件信息可保存")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(file_info_list)
    
    # 重新排列列的顺序
    column_order = [
        '患者名称', '子文件夹路径', '文件名', '文件扩展名',
        '解析_姓名', '解析_时间', '解析_时间_格式化', '第2个dash后内容', '序列类型',
        '解析_所有部分', '解析_部分数量',
        '文件大小(MB)', '相对路径', '完整路径'
    ]
    
    # 只保留存在的列
    existing_columns = [col for col in column_order if col in df.columns]
    df = df[existing_columns]
    
    try:
        # 保存到Excel文件
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 主表：所有文件
            df.to_excel(writer, sheet_name='所有文件', index=False)
            
            # 分表：按患者名称分组
            for patient_name in df['患者名称'].unique():
                patient_df = df[df['患者名称'] == patient_name]
                sheet_name = patient_name[:31]  # Excel工作表名称限制31个字符
                patient_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 统计表
            create_summary_sheet(df, writer)
        
        print(f"文件信息已保存到: {output_path}")
        print(f"总共处理了 {len(df)} 个文件")
        print(f"涉及 {df['患者名称'].nunique()} 个患者")
        
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")

def create_summary_sheet(df, writer):
    """
    创建统计汇总表
    
    Args:
        df: 主数据DataFrame
        writer: Excel writer对象
    """
    summary_data = []
    
    # 按患者统计
    for patient_name in df['患者名称'].unique():
        patient_df = df[df['患者名称'] == patient_name]
        
        # 统计信息
        total_files = len(patient_df)
        nii_files = len(patient_df[patient_df['文件扩展名'] == '.gz'])
        subfolders = patient_df['子文件夹路径'].nunique()
        total_size = patient_df['文件大小(MB)'].sum()
        
        # 时间点统计
        time_points = patient_df['解析_时间'].dropna().nunique()
        
        summary_data.append({
            '患者名称': patient_name,
            '总文件数': total_files,
            'NII文件数': nii_files,
            '子文件夹数': subfolders,
            '时间点数': time_points,
            '总大小(MB)': round(total_size, 2)
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_excel(writer, sheet_name='患者统计', index=False)

def main():
    # 设置路径
    base_directory = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image"
    output_excel = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\患者文件夹文件名列表.xlsx"
    
    print("患者文件夹文件名扫描工具")
    print("=" * 50)
    print(f"扫描目录: {base_directory}")
    print(f"输出文件: {output_excel}")
    print()
    
    # 检查目录是否存在
    if not os.path.exists(base_directory):
        print(f"错误: 目录 {base_directory} 不存在")
        print("请检查路径是否正确")
        return
    
    # 扫描文件
    print("开始扫描文件...")
    file_info_list = scan_patient_folders(base_directory)
    
    if not file_info_list:
        print("没有找到任何文件")
        return
    
    # 保存到Excel
    print(f"\n找到 {len(file_info_list)} 个文件，正在保存到Excel...")
    save_to_excel(file_info_list, output_excel)
    
    # 显示简要统计
    df = pd.DataFrame(file_info_list)
    print(f"\n扫描完成!")
    print(f"患者数量: {df['患者名称'].nunique()}")
    print(f"文件总数: {len(df)}")
    print(f"NII文件数: {len(df[df['文件扩展名'] == '.gz'])}")
    
    # 显示患者列表
    print(f"\n患者列表:")
    for i, patient in enumerate(sorted(df['患者名称'].unique()), 1):
        file_count = len(df[df['患者名称'] == patient])
        print(f"  {i:2d}. {patient} ({file_count} 个文件)")

if __name__ == "__main__":
    main()
