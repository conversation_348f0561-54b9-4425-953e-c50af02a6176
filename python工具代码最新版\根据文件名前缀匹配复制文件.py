import os
import shutil
from pathlib import Path

def copy_matching_files():
    """
    根据文件名前缀匹配，将pp文件夹中的文件复制到pp2文件夹中
    """
    # 定义路径
    dwi_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\dwi"
    pp_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\pp"
    pp2_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\598HCC\image\pp2"
    
    # 检查源路径是否存在
    if not os.path.exists(dwi_path):
        print(f"DWI路径不存在: {dwi_path}")
        return
    
    if not os.path.exists(pp_path):
        print(f"PP路径不存在: {pp_path}")
        return
    
    # 创建目标文件夹pp2
    os.makedirs(pp2_path, exist_ok=True)
    print(f"创建目标文件夹: {pp2_path}")
    
    # 获取dwi文件夹中所有文件的前缀（-前面的字符）
    dwi_prefixes = set()
    try:
        for filename in os.listdir(dwi_path):
            if os.path.isfile(os.path.join(dwi_path, filename)):
                # 提取文件名中-前面的字符
                if '-' in filename:
                    prefix = filename.split('-')[0]
                    dwi_prefixes.add(prefix)
                    print(f"DWI文件前缀: {prefix} (来自文件: {filename})")
    except Exception as e:
        print(f"读取DWI文件夹时出错: {e}")
        return
    
    if not dwi_prefixes:
        print("DWI文件夹中没有找到包含'-'的文件")
        return
    
    print(f"共找到 {len(dwi_prefixes)} 个DWI文件前缀: {sorted(dwi_prefixes)}")
    
    # 遍历pp文件夹中的文件
    copied_count = 0
    try:
        for filename in sorted(os.listdir(pp_path)):
            file_path = os.path.join(pp_path, filename)
            if os.path.isfile(file_path):
                # 提取文件名中-前面的字符
                if '-' in filename:
                    prefix = filename.split('-')[0]
                    
                    # 检查前缀是否在dwi_prefixes中
                    if prefix in dwi_prefixes:
                        # 复制文件到pp2文件夹
                        dest_path = os.path.join(pp2_path, filename)
                        try:
                            shutil.copy2(file_path, dest_path)
                            print(f"已复制: {filename} -> pp2/")
                            copied_count += 1
                        except Exception as e:
                            print(f"复制文件 {filename} 时出错: {e}")
                    else:
                        print(f"跳过文件: {filename} (前缀 '{prefix}' 在DWI中不存在)")
                else:
                    print(f"跳过文件: {filename} (文件名中不包含'-')")
    except Exception as e:
        print(f"读取PP文件夹时出错: {e}")
        return
    
    print(f"\n复制完成！共复制了 {copied_count} 个文件到 {pp2_path}")

if __name__ == "__main__":
    copy_matching_files()
