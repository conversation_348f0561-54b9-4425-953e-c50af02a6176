import os
import glob

def batch_rename_nii_files(base_path):
    """
    批量重命名nii.gz文件
    重命名规则（保留前缀-患者姓名-日期）：
    BH-Ax-LAVA-Mask -> t1
    BH-Ax-LAVA+C-3A -> ap
    BH-Ax-LAVA+C-P -> pp
    RTr-Ax-T2-fs-Prop -> t2
    RTr-Ax-DWI-b50-800 -> dwi
    BH-Ax-LAVA+C-20min -> hbp
    ADC-(10^-6-mm²-s) -> adc
    """
    
    # 定义重命名映射规则
    rename_mapping = {
        "BH-Ax-LAVA-Mask": "t1",
        "BH-Ax-LAVA+C-3A": "ap",
        "Ph2-BH-Ax-LAVA+C-3A": "ap",
        "Ph1-ap": "ap",
        "Ph2-ap": "ap",
        "Ph3-ap": "ap",
        "BH-Ax-LAVA+C-P": "pp",
        "-pp": "pp",
        "RTr-Ax-T2-fs-Prop": "t2",
        "BH-Ax-T2-fs-Prop": "t2",
        "-t2": "t2",
        "RTr-Ax-DWI-0-50-800": "dwi",
        "RTr-Ax-DWI-b50-800": "dwi",
        "BH-Ax-LAVA+C-20min": "hbp",
        "BH-Ax-LAVA-HPB": "hbp",
        "ADC-(10^-6-mm²-s)": "adc",
        "ADC-(10^-6-mm�-s)": "adc",  # 处理特殊字符显示问题
        "-adc": "adc",
        "Ax-IN": "inphase",
        "InPhase--BH-Ax-LAVA-Flex": "inphase",
        "InPhase--BH-Ax-IDEAL-IQ": "inphase",
        "-inphase": "inphase",
        "Ax-OUT": "outphase",
        "OutPhase--BH-Ax-LAVA-Flex": "outphase",
        "OutPhase--BH-Ax-IDEAL-IQ": "outphase",
        "-outphase": "outphase",
        "WATER--BH-Ax-LAVA-Flex": "water",
        "WATER--BH-Ax-IDEAL-IQ": "water",
        "Ax-Water": "water",
        "-water": "water",
        "FAT--BH-Ax-LAVA-Flex": "fat",
        "FAT--BH-Ax-IDEAL-IQ": "fat",
        "-fat": "fat",
        "-t1": "t1"
    }
    
    # 检查路径是否存在
    if not os.path.exists(base_path):
        print(f"错误：路径不存在 {base_path}")
        return
    
    # 获取所有患者文件夹
    patient_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]
    patient_folders.sort()  # 按文件夹名排序
    
    print(f"找到 {len(patient_folders)} 个患者文件夹")
    
    total_renamed = 0
    
    # 遍历每个患者文件夹
    for patient_folder in patient_folders:
        patient_path = os.path.join(base_path, patient_folder)
        print(f"\n处理患者文件夹: {patient_folder}")
        
        # 获取该文件夹下的所有nii.gz文件
        nii_files = glob.glob(os.path.join(patient_path, "*.nii.gz"))
        
        if not nii_files:
            print(f"  - 未找到nii.gz文件")
            continue
            
        folder_renamed = 0
        
        # 遍历每个nii.gz文件
        for nii_file in nii_files:
            filename = os.path.basename(nii_file)
            filename_without_ext = filename.replace('.nii.gz', '')

            # 检查是否匹配重命名规则
            new_name = None
            for old_pattern, new_pattern in rename_mapping.items():
                if old_pattern in filename_without_ext:
                    # 提取前缀部分（患者姓名-日期）
                    # 找到old_pattern在文件名中的位置，保留之前的部分作为前缀
                    pattern_index = filename_without_ext.find(old_pattern)
                    if pattern_index > 0:
                        prefix = filename_without_ext[:pattern_index].rstrip('-')
                        new_name = f"{prefix}-{new_pattern}.nii.gz"
                    else:
                        new_name = f"{new_pattern}.nii.gz"
                    break
            
            if new_name:
                new_file_path = os.path.join(patient_path, new_name)
                
                # 检查新文件名是否已存在
                if os.path.exists(new_file_path):
                    print(f"  - 跳过 {filename} -> {new_name} (目标文件已存在)")
                    continue
                
                try:
                    # 执行重命名
                    os.rename(nii_file, new_file_path)
                    print(f"  - 重命名: {filename} -> {new_name}")
                    folder_renamed += 1
                    total_renamed += 1
                except Exception as e:
                    print(f"  - 重命名失败: {filename} -> {new_name}, 错误: {e}")
            else:
                print(f"  - 未匹配规则: {filename}")
        
        print(f"  - 该文件夹重命名文件数: {folder_renamed}")
    
    print(f"\n总计重命名文件数: {total_renamed}")

def preview_rename(base_path):
    """
    预览重命名操作，不实际执行重命名
    """
    
    rename_mapping = {
        "BH-Ax-LAVA-Mask": "t1",
        "BH-Ax-LAVA+C-3A": "ap",
        "Ph2-BH-Ax-LAVA+C-3A": "ap",
        "Ph1-ap": "ap",
        "Ph2-ap": "ap",
        "Ph3-ap": "ap",
        "BH-Ax-LAVA+C-P": "pp",
        "-pp": "pp",
        "RTr-Ax-T2-fs-Prop": "t2",
        "BH-Ax-T2-fs-Prop": "t2",
        "-t2": "t2",
        "RTr-Ax-DWI-0-50-800": "dwi",
        "RTr-Ax-DWI-b50-800": "dwi",
        "BH-Ax-LAVA+C-20min": "hbp",
        "BH-Ax-LAVA-HPB": "hbp",
        "ADC-(10^-6-mm²-s)": "adc",
        "ADC-(10^-6-mm�-s)": "adc",  # 处理特殊字符显示问题
        "-adc": "adc",
        "Ax-IN": "inphase",
        "InPhase--BH-Ax-LAVA-Flex": "inphase",
        "InPhase--BH-Ax-IDEAL-IQ": "inphase",
        "-inphase": "inphase",
        "Ax-OUT": "outphase",
        "OutPhase--BH-Ax-LAVA-Flex": "outphase",
        "OutPhase--BH-Ax-IDEAL-IQ": "outphase",
        "-outphase": "outphase",
        "WATER--BH-Ax-LAVA-Flex": "water",
        "WATER--BH-Ax-IDEAL-IQ": "water",
        "Ax-Water": "water",
        "-water": "water",
        "FAT--BH-Ax-LAVA-Flex": "fat",
        "FAT--BH-Ax-IDEAL-IQ": "fat",
        "-fat": "fat",
        "-t1": "t1"
    }
    
    if not os.path.exists(base_path):
        print(f"错误：路径不存在 {base_path}")
        return
    
    patient_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]
    patient_folders.sort()
    
    print("=== 重命名预览 ===")
    print(f"找到 {len(patient_folders)} 个患者文件夹")
    
    total_to_rename = 0
    
    for patient_folder in patient_folders:
        patient_path = os.path.join(base_path, patient_folder)
        print(f"\n患者文件夹: {patient_folder}")
        
        nii_files = glob.glob(os.path.join(patient_path, "*.nii.gz"))
        
        if not nii_files:
            print(f"  - 未找到nii.gz文件")
            continue
        
        folder_to_rename = 0
        
        for nii_file in nii_files:
            filename = os.path.basename(nii_file)
            filename_without_ext = filename.replace('.nii.gz', '')

            new_name = None
            for old_pattern, new_pattern in rename_mapping.items():
                if old_pattern in filename_without_ext:
                    # 提取前缀部分（患者姓名-日期）
                    # 找到old_pattern在文件名中的位置，保留之前的部分作为前缀
                    pattern_index = filename_without_ext.find(old_pattern)
                    if pattern_index > 0:
                        prefix = filename_without_ext[:pattern_index].rstrip('-')
                        new_name = f"{prefix}-{new_pattern}.nii.gz"
                    else:
                        new_name = f"{new_pattern}.nii.gz"
                    break
            
            if new_name:
                new_file_path = os.path.join(patient_path, new_name)
                if os.path.exists(new_file_path):
                    print(f"  - 将跳过: {filename} -> {new_name} (目标文件已存在)")
                else:
                    print(f"  - 将重命名: {filename} -> {new_name}")
                    folder_to_rename += 1
                    total_to_rename += 1
            else:
                print(f"  - 不匹配规则: {filename}")
        
        print(f"  - 该文件夹将重命名文件数: {folder_to_rename}")
    
    print(f"\n总计将重命名文件数: {total_to_rename}")

if __name__ == "__main__":
    # 设置基础路径
    base_path = r"M:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output\GE MEDICAL SYSTEMS - SIGNA Architect-GEHCGEHC"

    print("nii.gz文件批量重命名工具")
    print("重命名规则（保留前缀-患者姓名-日期）:")
    print("BH-Ax-LAVA-Mask -> 前缀-t1")
    print("Ph2-BH-Ax-LAVA+C-3A -> 前缀-ap")
    print("BH-Ax-LAVA+C-P -> 前缀-pp")
    print("RTr-Ax-T2-fs-Prop -> 前缀-t2")
    print("RTr-Ax-DWI-0-50-800 -> 前缀-dwi")
    print("BH-Ax-LAVA+C-20min -> 前缀-hbp")
    print("ADC-(10^-6-mm²-s) -> 前缀-adc")
    print("Ax-IN -> 前缀-inphase")
    print("Ax-OUT -> 前缀-outphase")
    print("WATER--BH-Ax-LAVA-Flex -> 前缀-water")
    print("FAT--BH-Ax-LAVA-Flex -> 前缀-fat")
    print("\n选择操作:")
    print("1. 预览重命名操作")
    print("2. 执行重命名操作")

    choice = input("\n请输入选择 (1或2): ").strip()

    if choice == "1":
        preview_rename(base_path)
    elif choice == "2":
        confirm = input("\n确认执行重命名操作？这将修改文件名！(y/N): ").strip().lower()
        if confirm == 'y':
            batch_rename_nii_files(base_path)
        else:
            print("操作已取消")
    else:
        print("无效选择")
