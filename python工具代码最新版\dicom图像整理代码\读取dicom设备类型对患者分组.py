#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的DICOM设备分组脚本
不依赖pydicom，直接读取DICOM文件头获取设备信息
"""

import os
import struct
from collections import defaultdict

def is_dicom_file(file_path):
    """检查文件是否为DICOM格式"""
    try:
        with open(file_path, 'rb') as f:
            f.seek(128)
            return f.read(4) == b'DICM'
    except:
        return False

def read_dicom_tag(file_path, group, element):
    """读取DICOM标签值 - 改进版本"""
    try:
        with open(file_path, 'rb') as f:
            # 检查DICOM标识
            f.seek(128)
            if f.read(4) != b'DICM':
                return None

            # 读取数据元素
            while True:
                pos = f.tell()
                tag_data = f.read(8)
                if len(tag_data) < 8:
                    break

                # 解析标签 (Group, Element)
                tag_group = struct.unpack('<H', tag_data[0:2])[0]
                tag_element = struct.unpack('<H', tag_data[2:4])[0]

                # 读取VR (Value Representation)
                vr = tag_data[4:6]

                # 根据VR确定长度字段的格式
                if vr in [b'OB', b'OW', b'OF', b'SQ', b'UT', b'UN']:
                    # 这些VR使用4字节长度
                    f.seek(pos + 8)
                    length_data = f.read(4)
                    if len(length_data) < 4:
                        break
                    length = struct.unpack('<I', length_data)[0]
                    value_start = pos + 12
                else:
                    # 其他VR使用2字节长度
                    length = struct.unpack('<H', tag_data[6:8])[0]
                    value_start = pos + 8

                if tag_group == group and tag_element == element:
                    # 找到目标标签，读取值
                    f.seek(value_start)
                    if length > 0 and length < 10000:  # 安全检查
                        value_data = f.read(length)
                        try:
                            # 尝试解码为文本
                            result = value_data.decode('utf-8', errors='ignore').strip('\x00').strip()
                            if result:
                                return result
                            # 如果UTF-8失败，尝试其他编码
                            result = value_data.decode('latin-1', errors='ignore').strip('\x00').strip()
                            return result if result else None
                        except:
                            return None
                    return None
                else:
                    # 跳过这个标签的值
                    f.seek(value_start + length)

        return None
    except Exception as e:
        print(f"读取DICOM标签错误 {file_path}: {e}")
        return None

def get_device_info_simple(file_path):
    """获取设备信息（简化版）"""
    if not is_dicom_file(file_path):
        return None
    
    # 常用的设备相关DICOM标签
    # (0008,0070) Manufacturer
    # (0008,1090) Manufacturer's Model Name  
    # (0018,1000) Device Serial Number
    # (0008,1010) Station Name
    
    manufacturer = read_dicom_tag(file_path, 0x0008, 0x0070)
    model_name = read_dicom_tag(file_path, 0x0008, 0x1090)
    serial_number = read_dicom_tag(file_path, 0x0018, 0x1000)
    station_name = read_dicom_tag(file_path, 0x0008, 0x1010)
    
    return {
        'manufacturer': manufacturer or 'Unknown',
        'model_name': model_name or 'Unknown',
        'serial_number': serial_number or 'Unknown',
        'station_name': station_name or 'Unknown'
    }

def find_first_dicom(patient_folder):
    """在患者文件夹中查找第一个DICOM文件"""
    for root, _, files in os.walk(patient_folder):
        for file in files:
            file_path = os.path.join(root, file)
            if is_dicom_file(file_path):
                return file_path
    return None

def process_patients_simple(base_path):
    """处理所有患者文件夹"""
    patient_device_info = {}
    
    # 获取所有患者文件夹
    patient_folders = [f for f in os.listdir(base_path) 
                      if os.path.isdir(os.path.join(base_path, f))]
    
    print(f"找到 {len(patient_folders)} 个患者文件夹")
    
    for i, patient_name in enumerate(patient_folders, 1):
        patient_path = os.path.join(base_path, patient_name)
        print(f"处理患者 {i}/{len(patient_folders)}: {patient_name}")
        
        # 查找第一个DICOM文件
        dicom_file = find_first_dicom(patient_path)
        
        if dicom_file:
            # 获取设备信息
            device_info = get_device_info_simple(dicom_file)
            if device_info:
                patient_device_info[patient_name] = device_info
                print(f"  设备: {device_info['manufacturer']} {device_info['model_name']}")
            else:
                print(f"  无法获取设备信息")
        else:
            print(f"  未找到DICOM文件")
    
    return patient_device_info

def save_results_simple(device_groups, excel_file):
    """保存结果到Excel文件"""
    # 保存Excel文件
    try:
        import pandas as pd

        # 创建详细信息数据
        all_patients_data = []
        for device_type, patients in device_groups.items():
            for patient in patients:
                all_patients_data.append({
                    '患者姓名': patient['patient_name'],
                    '设备类型': device_type,
                    '制造商': patient['device_info']['manufacturer'],
                    '设备型号': patient['device_info']['model_name'],
                    '设备序列号': patient['device_info']['serial_number'],
                    '工作站名称': patient['device_info']['station_name']
                })

        # 保存到Excel - 只保留患者详细信息
        detail_df = pd.DataFrame(all_patients_data)
        detail_df.to_excel(excel_file, index=False)

        print(f"Excel结果已保存到: {excel_file}")

    except ImportError:
        print("pandas未安装，跳过Excel文件生成")
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")

def main():
    """主函数"""
    # 配置路径
    base_path = r"E:\瑞金普美显MRI病例\瑞金普美显MRI"
    excel_file = r"E:\瑞金普美显MRI病例\患者设备分组结果.xlsx"

    print(f"当前工作目录: {os.getcwd()}")
    print(f"目标路径: {os.path.abspath(base_path)}")
    print(f"Excel输出路径: {excel_file}")

    if not os.path.exists(base_path):
        print(f"路径不存在: {base_path}")
        return

    print(f"开始处理患者文件夹: {base_path}")

    # 先测试一个患者
    test_patient = "SUN_MINXIA"
    test_path = os.path.join(base_path, test_patient)
    if os.path.exists(test_path):
        print(f"\n测试患者: {test_patient}")
        dicom_file = find_first_dicom(test_path)
        if dicom_file:
            device_info = get_device_info_simple(dicom_file)
            if device_info:
                print(f"测试成功，设备信息: {device_info}")
            else:
                print("无法获取设备信息")
                return
        else:
            print("未找到DICOM文件")
            return

    # 处理所有患者
    patient_device_info = process_patients_simple(base_path)

    if not patient_device_info:
        print("未找到任何患者的设备信息")
        return

    print(f"\n成功获取 {len(patient_device_info)} 个患者的设备信息")

    # 根据设备类型分组
    device_groups = defaultdict(list)

    for patient_name, device_info in patient_device_info.items():
        device_key = f"{device_info['manufacturer']} - {device_info['model_name']}"
        device_groups[device_key].append({
            'patient_name': patient_name,
            'device_info': device_info
        })

    device_groups = dict(device_groups)

    print(f"\n发现 {len(device_groups)} 种不同的设备类型:")
    for device_type, patients in device_groups.items():
        print(f"  {device_type}: {len(patients)} 个患者")

    # 保存结果
    save_results_simple(device_groups, excel_file)

    print("\n处理完成!")

if __name__ == "__main__":
    main()
