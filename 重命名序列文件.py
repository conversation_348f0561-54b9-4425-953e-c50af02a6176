#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重命名序列文件脚本
将文件名从 "序列名-中文名.nii.gz" 格式重命名为 "中文名-序列名.nii.gz" 格式
"""

import os
import re
from pathlib import Path

def rename_sequence_files(base_path):
    """
    重命名序列文件
    
    Args:
        base_path (str): 包含序列文件夹的基础路径
    """
    base_path = Path(base_path)
    
    if not base_path.exists():
        print(f"错误：路径不存在 {base_path}")
        return
    
    # 统计信息
    total_renamed = 0
    total_skipped = 0
    
    print(f"开始处理路径: {base_path}")
    print("-" * 50)
    
    # 遍历所有子文件夹（序列文件夹）
    for sequence_folder in sorted(base_path.iterdir()):
        if not sequence_folder.is_dir():
            continue
            
        print(f"\n处理序列文件夹: {sequence_folder.name}")
        
        # 遍历文件夹中的所有 .nii.gz 文件
        nii_files = list(sequence_folder.glob("*.nii.gz"))
        
        if not nii_files:
            print(f"  - 未找到 .nii.gz 文件")
            continue
            
        for nii_file in sorted(nii_files):
            original_name = nii_file.name
            
            # 检查文件名格式：序列名-中文名.nii.gz
            # 使用正则表达式匹配格式
            pattern = r'^([A-Za-z0-9]+)-(.+)\.nii\.gz$'
            match = re.match(pattern, original_name)
            
            if not match:
                print(f"  - 跳过文件（格式不匹配）: {original_name}")
                total_skipped += 1
                continue
                
            sequence_name = match.group(1)  # 序列名（如 T1, T2, DWI, ADC）
            chinese_name = match.group(2)   # 中文名（如 陈愈伟）
            
            # 生成新文件名：中文名-序列名.nii.gz
            new_name = f"{chinese_name}-{sequence_name}.nii.gz"
            new_file_path = sequence_folder / new_name
            
            # 检查新文件名是否已存在
            if new_file_path.exists():
                print(f"  - 跳过文件（目标文件已存在）: {original_name} -> {new_name}")
                total_skipped += 1
                continue
            
            # 执行重命名
            try:
                nii_file.rename(new_file_path)
                print(f"  ✓ 重命名成功: {original_name} -> {new_name}")
                total_renamed += 1
            except Exception as e:
                print(f"  ✗ 重命名失败: {original_name} -> {new_name}")
                print(f"    错误: {str(e)}")
                total_skipped += 1
    
    print("\n" + "=" * 50)
    print(f"处理完成！")
    print(f"成功重命名: {total_renamed} 个文件")
    print(f"跳过文件: {total_skipped} 个文件")

def main():
    """主函数"""
    # 设置基础路径
    base_path = r"K:\肝脏MRI数据集\需要补充的T1-T2-DWI-ADC患者\nii_output"
    
    print("序列文件重命名工具")
    print("=" * 50)
    print(f"目标路径: {base_path}")
    print("重命名规则: 序列名-中文名.nii.gz -> 中文名-序列名.nii.gz")
    print("例如: T1-陈愈伟.nii.gz -> 陈愈伟-T1.nii.gz")
    print()
    
    # 确认是否继续
    response = input("是否继续执行重命名操作？(y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("操作已取消")
        return
    
    # 执行重命名
    rename_sequence_files(base_path)

if __name__ == "__main__":
    main()
