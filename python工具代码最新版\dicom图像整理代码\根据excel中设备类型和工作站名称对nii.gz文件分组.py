#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据Excel文件中的设备信息对患者文件夹进行分组
将设备类型和工作站名称一致的患者文件夹放到同一组中
组名格式：设备类型-工作站名称
"""

import os
import shutil
import pandas as pd
from pathlib import Path
from collections import defaultdict

def read_device_info(excel_file):
    """
    读取Excel文件中的设备信息
    
    Args:
        excel_file (str): Excel文件路径
        
    Returns:
        dict: 患者名称到设备信息的映射
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 检查必要的列是否存在
        required_columns = ['患者姓名', '设备类型', '工作站名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误：Excel文件缺少必要的列: {missing_columns}")
            return None
        
        # 创建患者到设备信息的映射
        patient_device_map = {}
        
        for _, row in df.iterrows():
            patient_name = str(row['患者姓名']).strip()
            device_type = str(row['设备类型']).strip()
            station_name = str(row['工作站名称']).strip()
            
            # 创建组名：设备类型-工作站名称
            group_name = f"{device_type}-{station_name}"
            
            patient_device_map[patient_name] = {
                'device_type': device_type,
                'station_name': station_name,
                'group_name': group_name
            }
        
        print(f"成功读取 {len(patient_device_map)} 个患者的设备信息")
        return patient_device_map
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def create_device_groups(patient_device_map, base_path):
    """
    根据设备信息创建患者分组

    Args:
        patient_device_map (dict): 患者设备信息映射
        base_path (str): 患者文件夹基础路径（在此路径下进行分组）
    """
    base_path = Path(base_path)

    if not base_path.exists():
        print(f"错误：路径不存在 {base_path}")
        return
    
    # 按组名分组患者
    groups = defaultdict(list)

    # 获取所有患者文件夹
    patient_folders = [f for f in base_path.iterdir() if f.is_dir()]

    print(f"找到 {len(patient_folders)} 个患者文件夹")

    # 统计信息
    matched_patients = 0
    unmatched_patients = []

    for patient_folder in patient_folders:
        patient_name = patient_folder.name

        if patient_name in patient_device_map:
            device_info = patient_device_map[patient_name]
            group_name = device_info['group_name']
            groups[group_name].append({
                'patient_name': patient_name,
                'patient_folder': patient_folder,
                'device_info': device_info
            })
            matched_patients += 1
        else:
            unmatched_patients.append(patient_name)
    
    print(f"\n匹配到设备信息的患者: {matched_patients} 个")
    print(f"未匹配到设备信息的患者: {len(unmatched_patients)} 个")
    
    if unmatched_patients:
        print("未匹配的患者列表:")
        for patient in unmatched_patients[:10]:  # 只显示前10个
            print(f"  - {patient}")
        if len(unmatched_patients) > 10:
            print(f"  ... 还有 {len(unmatched_patients) - 10} 个")
    
    # 创建分组文件夹并移动患者数据
    print(f"\n开始创建 {len(groups)} 个设备分组:")

    for group_name, patients in groups.items():
        print(f"\n处理分组: {group_name} ({len(patients)} 个患者)")

        # 创建分组文件夹
        group_folder = base_path / group_name
        group_folder.mkdir(exist_ok=True)

        # 移动每个患者的文件夹
        for patient_info in patients:
            patient_name = patient_info['patient_name']
            source_folder = patient_info['patient_folder']
            target_folder = group_folder / patient_name

            try:
                if target_folder.exists():
                    print(f"  - 跳过 {patient_name} (目标文件夹已存在)")
                    continue

                # 移动整个患者文件夹
                shutil.move(str(source_folder), str(target_folder))
                print(f"  ✓ 移动成功: {patient_name}")

            except Exception as e:
                print(f"  ✗ 移动失败: {patient_name}")
                print(f"    错误: {str(e)}")
    
    # 输出分组统计
    print("\n" + "=" * 60)
    print("分组统计:")
    for group_name, patients in groups.items():
        print(f"  {group_name}: {len(patients)} 个患者")
    
    print(f"\n分组完成！共创建 {len(groups)} 个设备分组")

def main():
    """主函数"""
    # 配置路径
    excel_file = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\患者设备分组结果.xlsx"
    base_path = r"K:\肝脏MRI数据集\HCC-EOBMRI\HCC-ruijin\nii_output"

    print("患者文件夹设备分组工具")
    print("=" * 60)
    print(f"Excel文件: {excel_file}")
    print(f"工作路径: {base_path}")
    print(f"分组规则: 设备类型-工作站名称")
    print(f"操作方式: 在原路径下创建分组文件夹并移动患者文件夹")
    print()

    # 检查文件和路径是否存在
    if not os.path.exists(excel_file):
        print(f"错误：Excel文件不存在 {excel_file}")
        return

    if not os.path.exists(base_path):
        print(f"错误：工作路径不存在 {base_path}")
        return
    
    # 确认是否继续
    response = input("是否继续执行分组操作？(y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("操作已取消")
        return
    
    # 读取设备信息
    print("\n读取Excel文件中的设备信息...")
    patient_device_map = read_device_info(excel_file)
    
    if not patient_device_map:
        print("无法读取设备信息，操作终止")
        return
    
    # 显示设备分组预览
    groups_preview = defaultdict(int)
    for _, device_info in patient_device_map.items():
        groups_preview[device_info['group_name']] += 1

    print(f"\n预计将创建 {len(groups_preview)} 个设备分组:")
    for group_name, count in sorted(groups_preview.items()):
        print(f"  {group_name}: {count} 个患者")

    print()
    final_confirm = input("确认开始分组操作？(y/n): ").strip().lower()
    if final_confirm not in ['y', 'yes', '是']:
        print("操作已取消")
        return

    # 执行分组
    create_device_groups(patient_device_map, base_path)

if __name__ == "__main__":
    main()
