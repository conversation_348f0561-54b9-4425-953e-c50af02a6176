#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nnUNet模型预测脚本
基于nnUNet v2项目代码整合

功能:
1. 加载训练好的nnUNet模型
2. 批量预测处理
3. 支持checkpoint文件绝对路径
4. 自动检测训练器类型
5. 结果后处理和保存
6. 预测结果评估

作者: 基于nnUNet项目代码整合
"""
# 确保已安装nnUNet: pip install nnunetv2
# 确保已安装依赖: pip install SimpleITK
# 需要完整的训练结果文件夹，包含checkpoint_final.pth文件，否则会报错

##报错：# RuntimeError: Background workers died. Look for the error message further up! 
# If there is none then your RAM was full and the worker was killed by the OS. Use fewer workers or get more 
# 解决办法：数据维度不对，把图像数据维度调整到3维


import os
# 在导入其他库之前设置环境变量，解决OpenMP库冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'

import sys
import subprocess
import logging
import threading
import time
import re
from pathlib import Path
from typing import List, Tuple
import numpy as np
import SimpleITK as sitk
import nibabel as nib

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nnunet_prediction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class nnUNetPredictor:
    """nnUNet模型预测类"""

    def __init__(self,
                 base_dir: str = "./nnUNet_workspace",
                 dataset_id: int = 100):
        """
        初始化nnUNet预测器

        参数:
            base_dir: nnUNet工作目录
            dataset_id: 数据集ID
        """
        self.base_dir = Path(base_dir)
        self.dataset_id = dataset_id

        # nnUNet目录结构
        self.nnunet_raw = self.base_dir / "nnUNet_raw"
        self.nnunet_preprocessed = self.base_dir / "nnUNet_preprocessed"
        self.nnunet_results = self.base_dir / "nnUNet_results"

        self._setup_environment()
        self._validate_model_exists()

    def _clean_path(self, path_str: str) -> str:
        """清理路径中的特殊字符"""
        # 移除零宽字符和其他特殊字符
        import re
        cleaned = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', path_str)
        return cleaned
    
    def _setup_environment(self):
        """设置nnUNet环境变量"""
        env_vars = {
            'nnUNet_raw': str(self.nnunet_raw),
            'nnUNet_preprocessed': str(self.nnunet_preprocessed),
            'nnUNet_results': str(self.nnunet_results)
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
    
    def _validate_model_exists(self):
        """验证训练好的模型是否存在"""
        # 尝试多种可能的目录名格式
        possible_patterns = [
            f"Dataset{self.dataset_id:03d}_*",  # Dataset120_*
            f"Dataset{self.dataset_id}_*",      # Dataset120_*
        ]

        matching_dirs = []
        for pattern in possible_patterns:
            dataset_results_dir = self.nnunet_results / pattern
            import glob
            dirs = glob.glob(str(dataset_results_dir))
            matching_dirs.extend(dirs)

        # 去重
        matching_dirs = list(set(matching_dirs))

        if not matching_dirs:
            logger.warning(f"未找到数据集 {self.dataset_id} 的训练结果")
            logger.warning(f"请确保已完成模型训练，结果应在: {self.nnunet_results}")
            # 列出实际存在的目录帮助调试
            if self.nnunet_results.exists():
                actual_dirs = [d.name for d in self.nnunet_results.iterdir() if d.is_dir() and not d.name.startswith('.')]
                logger.warning(f"实际存在的目录: {actual_dirs}")
        else:
            self.model_dir = Path(matching_dirs[0])
            logger.info(f"找到模型目录: {self.model_dir}")
    
    def fix_image_dimensions(self,
                            image_dir: str,
                            output_dir: str = None):
        """
        修复图像维度问题，将4D或5D图像转换为3D图像

        参数:
            image_dir: 图像目录路径
            output_dir: 输出目录路径，如果为None则覆盖原文件
        """
        logger.info("开始修复图像维度...")

        if output_dir is None:
            output_dir = image_dir

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 获取所有 .nii.gz 文件并排序
        nii_files = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])

        if not nii_files:
            logger.warning(f"在 {image_dir} 中未找到.nii.gz文件")
            return

        logger.info(f"找到 {len(nii_files)} 个图像文件需要检查维度")

        fixed_count = 0
        skipped_count = 0

        # 遍历排序后的文件
        for i, filename in enumerate(nii_files):
            try:
                # 加载图像
                img_path = os.path.join(image_dir, filename)
                img = nib.load(img_path)

                # 获取图像数据
                image_data = img.get_fdata()

                original_shape = image_data.shape
                original_ndim = image_data.ndim

                # 只保留前三个维度
                if image_data.ndim > 3:
                    # 逐步剥离多余的维度，直到只剩下 3 个维度
                    while image_data.ndim > 3:
                        # 取最后一维的第 0 个索引，相当于丢弃该维度
                        image_data = image_data[..., 0]

                    # 创建新的 NIfTI 图像对象
                    new_img = nib.Nifti1Image(image_data, img.affine, img.header)

                    # 保存新的图像
                    new_filename = os.path.join(output_dir, filename)
                    nib.save(new_img, new_filename)

                    fixed_count += 1
                    logger.info(f"[{i+1}/{len(nii_files)}] 修复维度 {filename}: {original_shape} -> {image_data.shape} (维度: {original_ndim} -> {image_data.ndim})")
                else:
                    # 如果维度正确，且输出目录不同，则复制文件
                    if output_dir != image_dir:
                        import shutil
                        shutil.copy2(img_path, os.path.join(output_dir, filename))

                    skipped_count += 1
                    logger.info(f"[{i+1}/{len(nii_files)}] 跳过 {filename}: 维度正确 {original_shape} (维度: {original_ndim})")

            except Exception as e:
                logger.error(f"处理文件 {filename} 时出错: {e}")
                continue

        logger.info(f"维度修复完成: 修复了 {fixed_count} 个文件，跳过了 {skipped_count} 个文件")
        return output_dir

    def prepare_test_images(self,
                           input_folder: str,
                           output_folder: str,
                           add_channel_suffix: bool = True,
                           skip_existing: bool = True):
        """
        准备测试图像，转换为nnUNet预测格式
        优化版本：创建符号链接而不是复制文件，节省时间和空间

        参数:
            input_folder: 输入图像文件夹
            output_folder: 输出文件夹
            add_channel_suffix: 是否添加_0000后缀
            skip_existing: 是否跳过已存在的文件
        """
        logger.info("准备测试图像...")

        input_path = Path(input_folder)
        output_path = Path(output_folder)
        # 只在目录不存在时创建，避免重新创建现有目录
        if not output_path.exists():
            output_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建输出目录: {output_path}")
        else:
            logger.info(f"使用现有输出目录: {output_path}")

        # 获取所有.nii.gz文件
        image_files = sorted(list(input_path.glob("*.nii.gz")))

        if not image_files:
            # 提供更详细的错误信息和建议
            logger.error(f"在 {input_folder} 中未找到.nii.gz文件")
            logger.error("请检查以下几点:")
            logger.error("1. 路径是否正确")
            logger.error("2. 文件夹是否存在")
            logger.error("3. 文件夹中是否有.nii.gz格式的文件")

            # 检查路径是否存在
            if not input_path.exists():
                logger.error(f"路径不存在: {input_path}")
                # 尝试找到相似的路径
                parent_path = input_path.parent
                if parent_path.exists():
                    subdirs = [d.name for d in parent_path.iterdir() if d.is_dir()]
                    logger.error(f"父目录 {parent_path} 中的子文件夹: {subdirs}")
            else:
                # 路径存在但没有.nii.gz文件，列出实际文件
                all_files = list(input_path.glob("*"))
                if all_files:
                    file_types = {}
                    for file in all_files:
                        if file.is_file():
                            ext = file.suffix.lower()
                            file_types[ext] = file_types.get(ext, 0) + 1
                    logger.error(f"文件夹中的文件类型统计: {file_types}")

                    # 检查是否有其他医学图像格式
                    medical_formats = ['.nii', '.dcm', '.mhd', '.mha']
                    found_medical = []
                    for fmt in medical_formats:
                        files = list(input_path.glob(f"*{fmt}"))
                        if files:
                            found_medical.append(f"{fmt}: {len(files)}个文件")

                    if found_medical:
                        logger.error(f"发现其他医学图像格式: {', '.join(found_medical)}")
                        logger.error("建议: 将文件转换为.nii.gz格式")
                else:
                    logger.error("文件夹为空")

            raise ValueError(f"在 {input_folder} 中未找到.nii.gz文件")

        logger.info(f"找到 {len(image_files)} 个图像文件")

        processed_count = 0
        skipped_count = 0

        for i, img_file in enumerate(image_files):
            if add_channel_suffix and not img_file.name.endswith("_0000.nii.gz"):
                # 添加_0000后缀
                base_name = img_file.stem.replace('.nii', '')
                new_name = f"{base_name}_0000.nii.gz"
            else:
                new_name = img_file.name

            output_file = output_path / new_name

            # 检查是否跳过已存在的文件
            if skip_existing and output_file.exists():
                logger.info(f"跳过已存在的文件 {i+1}/{len(image_files)}: {new_name}")
                skipped_count += 1
                continue

            # 创建符号链接而不是复制文件（节省时间和空间）
            try:
                # 尝试创建符号链接
                output_file.symlink_to(img_file.resolve())
                logger.info(f"创建符号链接 {i+1}/{len(image_files)}: {new_name}")
            except (OSError, NotImplementedError):
                # 如果符号链接失败，则复制文件
                import shutil
                shutil.copy2(img_file, output_file)
                logger.info(f"复制图像 {i+1}/{len(image_files)}: {new_name}")
            processed_count += 1

        logger.info(f"测试图像准备完成，保存到: {output_path}")
        logger.info(f"处理了 {processed_count} 个文件，跳过了 {skipped_count} 个已存在的文件")
        return str(output_path)

    def _get_checkpoint_path(self, configuration: str, fold: int, checkpoint_name: str) -> str:
        """
        构建checkpoint文件的绝对路径

        参数:
            configuration: 配置名称
            fold: 折数
            checkpoint_name: checkpoint文件名

        返回:
            checkpoint文件的绝对路径
        """
        # 从已找到的模型目录中构建checkpoint路径
        if hasattr(self, 'model_dir'):
            # 查找训练器目录
            trainer_pattern = os.path.join(
                str(self.model_dir),  # 确保转换为字符串
                f"nnUNetTrainer*__nnUNetPlans__{configuration}",
                f"fold_{fold}",
                checkpoint_name
            )

            import glob
            matching_files = glob.glob(trainer_pattern)
            if matching_files:
                checkpoint_path = matching_files[0]  # 使用第一个匹配的文件
                logger.info(f"找到checkpoint文件: {checkpoint_path}")

                # 从路径中提取训练器名称
                trainer_dir = os.path.dirname(os.path.dirname(checkpoint_path))
                trainer_name = os.path.basename(trainer_dir).split('__')[0]

                return checkpoint_path, trainer_name
            else:
                # 如果没找到，列出实际存在的目录帮助调试
                logger.warning(f"未找到匹配的checkpoint文件，搜索模式: {trainer_pattern}")
                if os.path.exists(str(self.model_dir)):
                    subdirs = [d for d in os.listdir(str(self.model_dir)) if os.path.isdir(os.path.join(str(self.model_dir), d))]
                    logger.warning(f"模型目录中的子目录: {subdirs}")

                    # 检查每个训练器目录
                    for subdir in subdirs:
                        if f"__nnUNetPlans__{configuration}" in subdir:
                            fold_dir = os.path.join(str(self.model_dir), subdir, f"fold_{fold}")
                            if os.path.exists(fold_dir):
                                files = os.listdir(fold_dir)
                                logger.warning(f"  {fold_dir}: {files}")
        else:
            logger.warning("model_dir 属性不存在")

        # 如果没有找到，抛出错误
        raise FileNotFoundError(f"未找到checkpoint文件: {checkpoint_name} 在配置 {configuration} fold {fold}")

    def predict_batch(self,
                      input_folder: str,
                      output_folder: str,
                      configuration: str = "3d_fullres",
                      folds: Tuple[int, ...] = (0,),
                      checkpoint_name: str = "checkpoint_final.pth",
                      save_probabilities: bool = False,
                      num_processes: int = 2,
                      skip_existing: bool = True):
        """
        批量预测图像

        参数:
            input_folder: 输入图像文件夹
            output_folder: 输出结果文件夹
            configuration: 配置名称
            folds: 使用的模型折数
            checkpoint_name: 检查点名称
            save_probabilities: 是否保存概率图
            num_processes: 并行进程数
            skip_existing: 是否跳过已预测的文件
        """
        logger.info(f"开始批量预测，输入文件夹: {input_folder}")

        # 创建输出目录
        output_path = Path(output_folder)
        # 只在目录不存在时创建，避免重新创建现有目录
        if not output_path.exists():
            output_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建预测输出目录: {output_path}")
        else:
            logger.info(f"使用现有预测输出目录: {output_path}")

        # 获取输入文件列表
        input_files = sorted(list(Path(input_folder).glob("*.nii.gz")))
        total_files = len(input_files)

        # 检查已预测的文件
        if skip_existing:
            files_to_predict = []
            skipped_files = []

            for input_file in input_files:
                # 生成对应的输出文件名（去掉_0000后缀）
                output_name = input_file.name.replace("_0000.nii.gz", ".nii.gz")
                output_file = output_path / output_name

                if output_file.exists():
                    skipped_files.append(input_file)
                    logger.info(f"跳过已预测的文件: {input_file.name} -> {output_name}")
                else:
                    files_to_predict.append(input_file)

            logger.info(f"总共 {total_files} 个文件，跳过 {len(skipped_files)} 个已预测的文件，需要预测 {len(files_to_predict)} 个文件")

            # 如果没有需要预测的文件，直接返回
            if not files_to_predict:
                logger.info("所有文件都已预测完成，无需重新预测")
                return str(output_path)

            # 创建临时输入文件夹，只包含需要预测的文件
            import tempfile
            import shutil
            temp_dir = tempfile.gettempdir()
            temp_input_folder = Path(temp_dir) / "nnunet_temp_input_for_prediction"

            # 清理旧的临时文件夹
            if temp_input_folder.exists():
                shutil.rmtree(temp_input_folder)
                logger.info(f"清理旧的临时文件夹: {temp_input_folder}")

            temp_input_folder.mkdir(exist_ok=True)

            # 复制需要预测的文件到临时文件夹
            for file_to_predict in files_to_predict:
                shutil.copy2(file_to_predict, temp_input_folder / file_to_predict.name)

            # 更新输入文件夹为临时文件夹
            input_folder = str(temp_input_folder)
            input_files = files_to_predict
            total_files = len(files_to_predict)

        logger.info(f"准备预测 {total_files} 个文件")

        # 检查是否已经是绝对路径
        if os.path.isabs(checkpoint_name):
            # 如果是绝对路径，验证文件存在
            if not os.path.exists(checkpoint_name):
                raise FileNotFoundError(f"Checkpoint文件不存在: {checkpoint_name}")

            checkpoint_path = checkpoint_name

            # 对于直接放在nnUNet_results下的checkpoint文件，我们需要特殊处理
            # 创建一个临时的标准目录结构
            temp_model_dir = os.path.join(os.path.dirname(checkpoint_path), f"Dataset{self.dataset_id}_HCC_Dataset", "nnUNetTrainer__nnUNetPlans__3d_fullres", f"fold_{folds[0]}")

            # 创建目录结构
            os.makedirs(temp_model_dir, exist_ok=True)

            # 复制checkpoint文件到标准位置
            temp_checkpoint_path = os.path.join(temp_model_dir, "checkpoint_final.pth")
            if not os.path.exists(temp_checkpoint_path):
                import shutil
                shutil.copy2(checkpoint_path, temp_checkpoint_path)
                logger.info(f"复制checkpoint文件到标准位置: {temp_checkpoint_path}")

            # 创建必要的dataset.json文件
            dataset_json_path = os.path.join(os.path.dirname(temp_model_dir), "dataset.json")
            if not os.path.exists(dataset_json_path):
                dataset_json = {
                    "channel_names": {"0": "CT"},
                    "labels": {"background": 0, "HCC": 1},
                    "numTraining": 100,
                    "file_ending": ".nii.gz"
                }
                import json
                with open(dataset_json_path, 'w') as f:
                    json.dump(dataset_json, f, indent=2)
                logger.info(f"创建dataset.json文件: {dataset_json_path}")

            trainer_name = "nnUNetTrainer"
            checkpoint_file_name = "checkpoint_final.pth"
            model_base_dir = None

            logger.info(f"使用绝对路径checkpoint文件: {checkpoint_path}")
            logger.info(f"使用训练器: {trainer_name}")
            logger.info(f"Checkpoint文件名: {checkpoint_file_name}")

        else:
            # 如果是相对路径，构建绝对路径
            checkpoint_path, trainer_name = self._get_checkpoint_path(configuration, folds[0], checkpoint_name)
            checkpoint_file_name = checkpoint_name
            model_base_dir = None
            logger.info(f"使用checkpoint文件: {checkpoint_path}")
            logger.info(f"使用训练器: {trainer_name}")

        try:
            # 清理路径中的特殊字符
            clean_input_folder = self._clean_path(str(input_folder))
            clean_output_folder = self._clean_path(str(output_folder))

            # 构建预测命令
            cmd = [
                "nnUNetv2_predict",
                "-i", clean_input_folder,
                "-o", clean_output_folder,
                "-d", str(self.dataset_id),
                "-c", configuration,
                "-tr", trainer_name,  # 使用实际的训练器名称
                "-f"] + [str(f) for f in folds] + [
                "-chk", checkpoint_file_name,  # 只使用文件名，不是绝对路径
                "-npp", "1",  # 强制使用单进程
                "-nps", "1",  # 强制使用单进程
                "--disable_tta"  # 禁用测试时增强以节省内存
            ]

            # 如果使用绝对路径，需要临时设置环境变量
            env = os.environ.copy()
            if model_base_dir:
                env['nnUNet_results'] = model_base_dir
            
            if save_probabilities:
                cmd.append("--save_probabilities")
            
            logger.info(f"执行预测命令: {' '.join(cmd)}")

            # 执行预测并实时显示进度
            if model_base_dir:
                # 使用自定义环境变量执行
                self._run_prediction_with_progress_custom_env(cmd, input_files, total_files, env)
            else:
                # 使用默认环境执行
                self._run_prediction_with_progress(cmd, input_files, total_files)

            logger.info("批量预测完成!")

            # 清理临时文件夹
            if skip_existing and 'temp_input_for_prediction' in input_folder:
                temp_folder = Path(input_folder)
                if temp_folder.exists():
                    import shutil
                    shutil.rmtree(temp_folder)
                    logger.info(f"清理临时文件夹: {temp_folder}")

            return str(output_path)

        except subprocess.CalledProcessError as e:
            logger.error(f"预测失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            # 清理临时文件夹
            if skip_existing and 'temp_input_for_prediction' in input_folder:
                temp_folder = Path(input_folder)
                if temp_folder.exists():
                    import shutil
                    shutil.rmtree(temp_folder)
                    logger.info(f"清理临时文件夹: {temp_folder}")
            raise
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_predict命令，请确保已正确安装nnUNet")
            # 清理临时文件夹
            if skip_existing and 'temp_input_for_prediction' in input_folder:
                temp_folder = Path(input_folder)
                if temp_folder.exists():
                    import shutil
                    shutil.rmtree(temp_folder)
                    logger.info(f"清理临时文件夹: {temp_folder}")
            raise

    def _run_prediction_with_progress(self, cmd: List[str], input_files: List[Path], total_files: int):
        """
        执行预测命令并实时显示进度

        参数:
            cmd: 预测命令
            input_files: 输入文件列表
            total_files: 总文件数
        """
        # 启动预测进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        completed_files = 0
        current_file = ""
        processed_files = set()  # 记录已处理的文件，避免重复计数

        # 实时读取输出并解析进度
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break

            if output:
                output = output.strip()

                # 检查是否开始预测新文件
                if "Predicting" in output and ":" in output:
                    # 提取文件名，格式通常是 "Predicting filename:"
                    parts = output.split("Predicting")
                    if len(parts) > 1:
                        file_part = parts[1].strip()
                        if ":" in file_part:
                            current_file = file_part.split(":")[0].strip()

                            # 只有当文件名不在已处理集合中时才计数
                            if current_file not in processed_files:
                                processed_files.add(current_file)
                                completed_files += 1

                                # 显示进度
                                progress_percent = (completed_files / total_files) * 100
                                logger.info(f"[{completed_files}/{total_files}] ({progress_percent:.1f}%) 正在预测: {current_file}")

                # 检查是否完成文件预测
                elif "done with" in output:
                    # 提取完成的文件名
                    parts = output.split("done with")
                    if len(parts) > 1:
                        finished_file = parts[1].strip()
                        logger.info(f"✓ 完成预测: {finished_file}")

                # 显示其他重要信息
                elif any(keyword in output.lower() for keyword in ["error", "warning", "failed"]):
                    logger.warning(f"预测输出: {output}")

        # 等待进程完成
        return_code = process.wait()

        if return_code != 0:
            raise subprocess.CalledProcessError(return_code, cmd)

        logger.info(f"所有 {total_files} 个文件预测完成!")

    def _get_prediction_files(self, input_folder: str, output_folder: str, skip_existing: bool = True, file_pattern: str = None):
        """
        获取需要预测的文件列表
        """
        from pathlib import Path

        input_path = Path(input_folder)
        output_path = Path(output_folder)

        # 获取所有输入文件
        input_files = []
        for ext in ["*.nii.gz", "*.nii"]:
            input_files.extend(input_path.glob(ext))

        # 如果指定了文件模式，进行过滤
        if file_pattern:
            filtered_files = []
            for input_file in input_files:
                if file_pattern.lower() in input_file.name.lower():
                    filtered_files.append(input_file)
            input_files = filtered_files
            logger.info(f"根据模式 '{file_pattern}' 过滤后，找到 {len(input_files)} 个匹配文件")

        if not skip_existing:
            return input_files

        # 过滤已存在的预测结果
        files_to_predict = []
        for input_file in input_files:
            # 构建对应的输出文件名 - nnUNet输出可能是.nii或.nii.gz格式
            input_name = input_file.name

            # 去掉_0000后缀，保持原有扩展名
            if input_name.endswith('_0000.nii.gz'):
                output_name = input_name.replace('_0000.nii.gz', '.nii.gz')
            elif input_name.endswith('_0000.nii'):
                output_name = input_name.replace('_0000.nii', '.nii.gz')
            elif input_name.endswith('.nii.gz'):
                output_name = input_name  # 保持原名
            else:
                output_name = input_name + '.gz'  # 添加.gz后缀

            output_file = output_path / output_name

            # 如果.nii.gz不存在，也检查.nii文件
            output_file_nii = output_path / output_name.replace('.nii.gz', '.nii')

            file_exists = output_file.exists() or output_file_nii.exists()

            if not file_exists:
                files_to_predict.append(input_file)
                logger.info(f"需要预测: {input_name} -> {output_name}")
            else:
                existing_file = output_file if output_file.exists() else output_file_nii
                logger.info(f"跳过已存在的预测结果: {input_name} -> {existing_file.name}")

        return files_to_predict

    def predict_batch_with_memory_optimization(self,
                                             input_folder: str,
                                             output_folder: str,
                                             configuration: str = "3d_fullres",
                                             folds: Tuple[int, ...] = (0,),
                                             checkpoint_name: str = "checkpoint_final.pth",
                                             save_probabilities: bool = False,
                                             skip_existing: bool = True,
                                             num_processes: int = 1,
                                             batch_size: int = 5):
        """
        内存优化的批量预测，分批处理文件以减少内存使用
        """
        import tempfile
        import shutil
        from pathlib import Path

        logger.info(f"开始内存优化批量预测，批次大小: {batch_size}")

        # 获取所有需要预测的文件，根据数据集ID确定文件模式
        file_pattern = None
        if hasattr(self, 'dataset_id'):
            if self.dataset_id == 504:  # pp数据集
                file_pattern = "-pp"
            elif self.dataset_id == 503:  # ap数据集
                file_pattern = "-ap"

        # 使用最终输出目录检查已存在的文件
        input_files = self._get_prediction_files(input_folder, output_folder, skip_existing, file_pattern)
        total_files = len(input_files)

        if total_files == 0:
            logger.info("所有文件都已预测完成，无需重新预测")
            return

        logger.info(f"实际需要预测的文件数量: {total_files}")
        for file in input_files:
            logger.info(f"待预测文件: {file.name}")

        logger.info(f"总共需要预测 {total_files} 个文件，将分 {(total_files + batch_size - 1) // batch_size} 批处理")

        # 分批处理
        for batch_idx in range(0, total_files, batch_size):
            batch_files = input_files[batch_idx:batch_idx + batch_size]
            batch_num = batch_idx // batch_size + 1
            total_batches = (total_files + batch_size - 1) // batch_size

            logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_files)} 个文件")

            # 创建临时批次目录
            with tempfile.TemporaryDirectory(prefix="nnunet_batch_") as temp_batch_dir:
                temp_input_dir = Path(temp_batch_dir) / "input"
                temp_output_dir = Path(temp_batch_dir) / "output"
                temp_input_dir.mkdir(parents=True)
                temp_output_dir.mkdir(parents=True)

                # 复制当前批次的文件到临时目录
                for file_path in batch_files:
                    shutil.copy2(file_path, temp_input_dir / file_path.name)

                # 对当前批次执行预测
                try:
                    self.predict_batch(
                        input_folder=str(temp_input_dir),
                        output_folder=str(temp_output_dir),
                        configuration=configuration,
                        folds=folds,
                        checkpoint_name=checkpoint_name,
                        save_probabilities=save_probabilities,
                        skip_existing=False,  # 临时目录中不需要跳过
                        num_processes=1  # 强制单进程
                    )

                    # 将预测结果复制到最终输出目录
                    os.makedirs(output_folder, exist_ok=True)
                    for result_file in temp_output_dir.glob("*.nii.gz"):
                        shutil.copy2(result_file, Path(output_folder) / result_file.name)

                    logger.info(f"第 {batch_num} 批预测完成")

                except Exception as e:
                    logger.error(f"第 {batch_num} 批预测失败: {e}")
                    # 继续处理下一批
                    continue

        logger.info(f"所有 {total_files} 个文件的分批预测完成!")

    def _run_prediction_with_progress_custom_env(self, cmd: List[str], input_files: List[Path], total_files: int, env: dict):
        """
        使用自定义环境变量执行预测命令并实时显示进度

        参数:
            cmd: 预测命令
            input_files: 输入文件列表
            total_files: 总文件数
            env: 环境变量字典
        """
        # 启动预测进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            env=env  # 使用自定义环境变量
        )

        completed_files = 0
        current_file = ""
        processed_files = set()  # 记录已处理的文件，避免重复计数

        # 实时读取输出并解析进度
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break

            if output:
                output = output.strip()

                # 检查是否开始预测新文件
                if "Predicting" in output and ":" in output:
                    # 提取文件名，格式通常是 "Predicting filename:"
                    parts = output.split("Predicting")
                    if len(parts) > 1:
                        file_part = parts[1].strip()
                        if ":" in file_part:
                            current_file = file_part.split(":")[0].strip()

                            # 只有当文件名不在已处理集合中时才计数
                            if current_file not in processed_files:
                                processed_files.add(current_file)
                                completed_files += 1

                                # 显示进度
                                progress_percent = (completed_files / total_files) * 100
                                logger.info(f"[{completed_files}/{total_files}] ({progress_percent:.1f}%) 正在预测: {current_file}")

                # 检查是否完成文件预测
                elif "done with" in output:
                    # 提取完成的文件名
                    parts = output.split("done with")
                    if len(parts) > 1:
                        finished_file = parts[1].strip()
                        logger.info(f"✓ 完成预测: {finished_file}")

                # 显示其他重要信息
                elif any(keyword in output.lower() for keyword in ["error", "warning", "failed"]):
                    logger.warning(f"预测输出: {output}")

        # 等待进程完成
        return_code = process.wait()

        if return_code != 0:
            raise subprocess.CalledProcessError(return_code, cmd)

        logger.info(f"所有 {total_files} 个文件预测完成!")

    def postprocess_predictions(self,
                               predictions_folder: str,
                               output_folder: str = None):
        """
        对预测结果进行后处理

        参数:
            predictions_folder: 预测结果文件夹
            output_folder: 后处理输出文件夹
        """
        logger.info("开始后处理预测结果...")

        if output_folder is None:
            output_folder = str(Path(predictions_folder).parent / "postprocessed")

        try:
            # 查找后处理配置文件
            postprocessing_file = None
            for root, dirs, files in os.walk(self.nnunet_results):
                for file in files:
                    if file == "postprocessing.pkl":
                        postprocessing_file = os.path.join(root, file)
                        break
                if postprocessing_file:
                    break

            if postprocessing_file:
                cmd = [
                    "nnUNetv2_apply_postprocessing",
                    "-i", predictions_folder,
                    "-o", output_folder,
                    "-pp_pkl_file", postprocessing_file
                ]

                logger.info(f"执行后处理命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                logger.info("后处理完成!")
                logger.info(f"后处理输出: {result.stdout}")
            else:
                logger.warning("未找到后处理配置文件，跳过后处理")

        except subprocess.CalledProcessError as e:
            logger.error(f"后处理失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_apply_postprocessing命令")

    def evaluate_predictions(self,
                            predictions_folder: str,
                            ground_truth_folder: str,
                            labels: List[int] = None):
        """
        评估预测结果

        参数:
            predictions_folder: 预测结果文件夹
            ground_truth_folder: 真实标签文件夹
            labels: 要评估的标签列表
        """
        logger.info("开始评估预测结果...")

        if labels is None:
            labels = [1]  # 默认评估标签1

        try:
            cmd = [
                "nnUNetv2_evaluate_folder",
                "-ref", ground_truth_folder,
                "-pred", predictions_folder,
                "-l"] + [str(l) for l in labels]

            logger.info(f"执行评估命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("评估完成!")
            logger.info(f"评估结果: {result.stdout}")

        except subprocess.CalledProcessError as e:
            logger.error(f"评估失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
        except FileNotFoundError:
            logger.error("未找到nnUNetv2_evaluate_folder命令")

    def calculate_dice_scores(self,
                             predictions_folder: str,
                             ground_truth_folder: str):
        """
        计算Dice系数

        参数:
            predictions_folder: 预测结果文件夹
            ground_truth_folder: 真实标签文件夹
        """
        logger.info("计算Dice系数...")

        pred_files = sorted(list(Path(predictions_folder).glob("*.nii.gz")))
        gt_files = sorted(list(Path(ground_truth_folder).glob("*.nii.gz")))

        if len(pred_files) != len(gt_files):
            logger.warning(f"预测文件数量({len(pred_files)})与真实标签数量({len(gt_files)})不匹配")

        dice_scores = []

        for pred_file, gt_file in zip(pred_files, gt_files):
            try:
                # 读取图像
                pred_img = sitk.ReadImage(str(pred_file))
                gt_img = sitk.ReadImage(str(gt_file))

                # 转换为numpy数组
                pred_array = sitk.GetArrayFromImage(pred_img)
                gt_array = sitk.GetArrayFromImage(gt_img)

                # 计算Dice系数
                dice = self._calculate_dice(pred_array, gt_array)
                dice_scores.append(dice)

                logger.info(f"文件 {pred_file.name}: Dice = {dice:.4f}")

            except Exception as e:
                logger.error(f"处理文件 {pred_file} 时出错: {e}")
                continue

        if dice_scores:
            mean_dice = np.mean(dice_scores)
            std_dice = np.std(dice_scores)
            logger.info(f"平均Dice系数: {mean_dice:.4f} ± {std_dice:.4f}")
            logger.info(f"最高Dice系数: {max(dice_scores):.4f}")
            logger.info(f"最低Dice系数: {min(dice_scores):.4f}")

        return dice_scores

    def _calculate_dice(self, pred: np.ndarray, gt: np.ndarray) -> float:
        """计算两个二值图像的Dice系数"""
        pred_binary = (pred > 0).astype(np.uint8)
        gt_binary = (gt > 0).astype(np.uint8)

        intersection = np.sum(pred_binary * gt_binary)
        union = np.sum(pred_binary) + np.sum(gt_binary)

        if union == 0:
            return 1.0  # 两个都是空的情况

        dice = 2.0 * intersection / union
        return dice

    def run_full_prediction_pipeline(self,
                                   input_folder: str,
                                   output_folder: str,
                                   ground_truth_folder: str = None,
                                   configuration: str = "3d_fullres",
                                   checkpoint_name: str = "checkpoint_final.pth",
                                   save_probabilities: bool = False,
                                   apply_postprocessing: bool = True,
                                   evaluate_results: bool = True,
                                   skip_existing: bool = True,
                                   num_processes: int = 1,
                                   batch_size: int = 5):
        """
        运行完整的预测流程

        参数:
            input_folder: 输入图像文件夹
            output_folder: 输出结果文件夹
            ground_truth_folder: 真实标签文件夹（用于评估）
            configuration: 配置名称
            save_probabilities: 是否保存概率图
            apply_postprocessing: 是否应用后处理
            evaluate_results: 是否评估结果
            skip_existing: 是否跳过已预测的文件
            num_processes: 并行进程数
        """
        logger.info("开始完整预测流程...")

        try:
            # 初始化临时目录变量
            import tempfile
            temp_dir = tempfile.gettempdir()

            # 1. 首先修复图像维度问题
            temp_fixed_input = str(Path(temp_dir) / "nnunet_temp_fixed_dimensions")
            fixed_input_folder = self.fix_image_dimensions(
                image_dir=input_folder,
                output_dir=temp_fixed_input
            )
            logger.info(f"图像维度修复完成，使用修复后的文件夹: {fixed_input_folder}")

            # 2. 检查输入文件格式，如果已经是正确格式则直接使用
            input_path = Path(fixed_input_folder)
            input_files = list(input_path.glob("*.nii.gz"))

            # 检查是否所有文件都有_0000后缀
            all_have_suffix = all(f.name.endswith("_0000.nii.gz") for f in input_files)

            if all_have_suffix:
                # 如果所有文件都有正确后缀，直接使用修复后的文件夹
                prepared_input = fixed_input_folder
                logger.info("输入文件格式正确，直接使用修复后的文件夹")
            else:
                # 检查是否大部分文件有正确后缀
                files_with_suffix = [f for f in input_files if f.name.endswith("_0000.nii.gz")]
                suffix_ratio = len(files_with_suffix) / len(input_files) if input_files else 0

                if suffix_ratio > 0.8:  # 如果80%以上的文件有正确后缀
                    # 直接使用修复后的文件夹，nnUNet会自动处理
                    prepared_input = fixed_input_folder
                    logger.info(f"大部分文件({suffix_ratio:.1%})格式正确，直接使用修复后的文件夹")
                else:
                    # 否则准备测试图像 - 使用系统临时目录
                    temp_prepared_input = str(Path(temp_dir) / "nnunet_temp_prepared_input")
                    prepared_input = self.prepare_test_images(
                        input_folder=fixed_input_folder,
                        output_folder=temp_prepared_input,
                        skip_existing=skip_existing
                    )

            # 2. 执行预测 - 使用内存优化的分批预测
            predictions_folder = output_folder
            self.predict_batch_with_memory_optimization(
                input_folder=prepared_input,
                output_folder=predictions_folder,
                configuration=configuration,
                checkpoint_name=checkpoint_name,
                save_probabilities=save_probabilities,
                skip_existing=skip_existing,
                num_processes=num_processes,
                batch_size=batch_size
            )

            # 3. 后处理（可选）
            final_predictions_folder = predictions_folder
            if apply_postprocessing:
                # 创建临时后处理目录，然后将结果移回原目录
                temp_postprocessed_folder = str(Path(temp_dir) / "nnunet_temp_postprocessed")
                self.postprocess_predictions(predictions_folder, temp_postprocessed_folder)

                # 将后处理结果移回原目录
                import shutil
                if Path(temp_postprocessed_folder).exists():
                    for file in Path(temp_postprocessed_folder).glob("*.nii.gz"):
                        shutil.move(str(file), str(Path(output_folder) / file.name))
                    # 清理临时目录
                    shutil.rmtree(temp_postprocessed_folder)
                    logger.info("后处理结果已移回原目录")

                final_predictions_folder = predictions_folder

            # 4. 评估结果（可选）
            if evaluate_results and ground_truth_folder:
                self.evaluate_predictions(final_predictions_folder, ground_truth_folder)
                self.calculate_dice_scores(final_predictions_folder, ground_truth_folder)

            logger.info("完整预测流程执行完成!")
            logger.info(f"最终结果保存在: {final_predictions_folder}")

            # 清理临时目录（只有在创建了临时目录时才清理）
            if 'temp_prepared_input' in locals() and Path(temp_prepared_input).exists():
                import shutil
                shutil.rmtree(temp_prepared_input)
                logger.info(f"清理临时准备目录: {temp_prepared_input}")

            # 清理维度修复临时目录
            if 'temp_fixed_input' in locals() and Path(temp_fixed_input).exists():
                import shutil
                shutil.rmtree(temp_fixed_input)
                logger.info(f"清理临时维度修复目录: {temp_fixed_input}")

            return final_predictions_folder

        except Exception as e:
            logger.error(f"预测流程执行失败: {e}")
            # 清理临时目录
            if 'temp_prepared_input' in locals() and Path(temp_prepared_input).exists():
                import shutil
                shutil.rmtree(temp_prepared_input)
                logger.info(f"清理临时准备目录: {temp_prepared_input}")

            # 清理维度修复临时目录
            if 'temp_fixed_input' in locals() and Path(temp_fixed_input).exists():
                import shutil
                shutil.rmtree(temp_fixed_input)
                logger.info(f"清理临时维度修复目录: {temp_fixed_input}")
            raise


def fix_image_dimensions_standalone(image_dir: str, output_dir: str = None):
    """
    独立的图像维度修复函数

    参数:
        image_dir: 图像目录路径
        output_dir: 输出目录路径，如果为None则覆盖原文件
    """
    print("开始修复图像维度...")

    if output_dir is None:
        output_dir = image_dir

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有 .nii.gz 文件并排序
    nii_files = sorted([filename for filename in os.listdir(image_dir) if filename.endswith('.nii.gz')])

    if not nii_files:
        print(f"在 {image_dir} 中未找到.nii.gz文件")
        return

    print(f"找到 {len(nii_files)} 个图像文件需要检查维度")

    fixed_count = 0
    skipped_count = 0

    # 遍历排序后的文件
    for i, filename in enumerate(nii_files):
        try:
            # 加载图像
            img_path = os.path.join(image_dir, filename)
            img = nib.load(img_path)

            # 获取图像数据
            image_data = img.get_fdata()

            original_shape = image_data.shape
            original_ndim = image_data.ndim

            # 只保留前三个维度
            if image_data.ndim > 3:
                # 逐步剥离多余的维度，直到只剩下 3 个维度
                while image_data.ndim > 3:
                    # 取最后一维的第 0 个索引，相当于丢弃该维度
                    image_data = image_data[..., 0]

                # 创建新的 NIfTI 图像对象
                new_img = nib.Nifti1Image(image_data, img.affine, img.header)

                # 保存新的图像
                new_filename = os.path.join(output_dir, filename)
                nib.save(new_img, new_filename)

                fixed_count += 1
                print(f"[{i+1}/{len(nii_files)}] 修复维度 {filename}: {original_shape} -> {image_data.shape} (维度: {original_ndim} -> {image_data.ndim})")
            else:
                # 如果维度正确，且输出目录不同，则复制文件
                if output_dir != image_dir:
                    import shutil
                    shutil.copy2(img_path, os.path.join(output_dir, filename))

                skipped_count += 1
                print(f"[{i+1}/{len(nii_files)}] 跳过 {filename}: 维度正确 {original_shape} (维度: {original_ndim})")

        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")
            continue

    print(f"维度修复完成: 修复了 {fixed_count} 个文件，跳过了 {skipped_count} 个文件")
    return output_dir


def main():
    """主函数 - 演示nnUNet预测流程"""

    # 配置参数 - 请根据您的数据修改这些路径
    config = {
        # 模型配置
        "base_dir": r"J:\nnUNet_workspace",
        "dataset_id": 505,  # 修改为实际存在的数据集ID

        # 预测数据路径
        "input_folder": r"K:\肝脏MRI数据集\HCC新增待整理\HCC2024-2025\hbp",
        "output_folder": r"K:\肝脏MRI数据集\HCC新增待整理\HCC2024-2025\hbp_prediction",
        "ground_truth_folder": None,  # 如果有真实标签用于评估，请指定路径

        # 预测配置（内存优化）
        "configuration": "3d_fullres",  # 应与训练时使用的配置一致
        "folds": (0,),  # 使用的模型折数
        "checkpoint_name": r"J:\nnUNet_workspace\nnUNet_results\Dataset505_hbp\nnUNetTrainer__nnUNetPlans__3d_fullres\fold_0\checkpoint_final.pth",  # 使用实际存在的checkpoint文件
        "save_probabilities": False,
        "apply_postprocessing": True,  # 暂时关闭后处理以节省内存
        "evaluate_results": False,  # 如果有真实标签则设为True
        "skip_existing": True,  # 是否跳过已预测的文件
        "num_processes": 1,  # 减少并行进程数以节省内存
        "batch_size": 2,  # 添加批次大小控制
    }

    try:
        # 检查CUDA可用性
        import torch
        if torch.cuda.is_available():
            logger.info(f"CUDA可用，GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            logger.info("CUDA不可用，将使用CPU进行预测")

        # 创建nnUNet预测器
        predictor = nnUNetPredictor(
            base_dir=config["base_dir"],
            dataset_id=config["dataset_id"]
        )

        # 运行完整预测流程
        result_folder = predictor.run_full_prediction_pipeline(
            input_folder=config["input_folder"],
            output_folder=config["output_folder"],
            ground_truth_folder=config["ground_truth_folder"],
            configuration=config["configuration"],
            checkpoint_name=config["checkpoint_name"],
            save_probabilities=config["save_probabilities"],
            apply_postprocessing=config["apply_postprocessing"],
            evaluate_results=config["evaluate_results"],
            skip_existing=config["skip_existing"],
            num_processes=config["num_processes"],
            batch_size=config["batch_size"]
        )

        logger.info("=" * 50)
        logger.info("nnUNet预测完成!")
        logger.info("=" * 50)
        logger.info(f"预测结果保存在: {result_folder}")
        logger.info(f"数据集ID: {config['dataset_id']}")

        # 如果有真实标签，计算Dice系数
        if config["ground_truth_folder"]:
            logger.info("计算预测准确性...")
            dice_scores = predictor.calculate_dice_scores(
                result_folder,
                config["ground_truth_folder"]
            )
            if dice_scores:
                logger.info(f"平均Dice系数: {np.mean(dice_scores):.4f}")

    except Exception as e:
        logger.error(f"预测执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

    return 0


if __name__ == "__main__":
    """
    使用说明:
    1. 确保已完成nnUNet模型训练
    2. 修改main()函数中的config字典，设置您的数据路径
    3. 确保已安装nnUNet: pip install nnunetv2
    4. 确保已安装依赖: pip install SimpleITK numpy nibabel
    5. 运行脚本: python nnunet_prediction.py

    预测数据格式要求:
    - 图像应该是.nii.gz格式
    - 图像应该是3D医学图像格式
    - 如果图像文件名不包含_0000后缀，脚本会自动添加
    - 脚本会自动修复4D/5D图像维度问题

    功能特性:
    - 批量预测多个图像
    - 支持checkpoint文件绝对路径
    - 自动检测训练器类型
    - 自动修复图像维度问题（4D/5D转3D）
    - 可选的后处理和评估功能
    - 智能跳过已预测的文件，避免重复预测
    - 设置ground_truth_folder可以评估预测准确性
    - 设置save_probabilities=True可以保存概率图
    - 设置apply_postprocessing=True可以应用后处理
    - 设置skip_existing=True可以跳过已预测的文件（默认开启）

    独立使用维度修复功能:
    from nnunet_prediction import fix_image_dimensions_standalone
    fix_image_dimensions_standalone('/path/to/images', '/path/to/output')
    """

    import sys

    # 环境变量已在脚本开头设置

    # 运行主预测流程
    exit_code = main()

    sys.exit(exit_code)
