#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内部验证与外部验证合并分析的可行性评估
Analysis of Pooling Internal and External Validation Cohorts
"""

import numpy as np
from scipy.stats import norm, chi2_contingency
from math import sqrt

def roc_power_analysis(auc, n_positive, n_negative, alpha=0.05):
    """计算ROC分析的统计功效"""
    n_total = n_positive + n_negative
    se_auc = sqrt(auc * (1 - auc) / n_total)
    z_observed = (auc - 0.5) / se_auc
    z_alpha = norm.ppf(1 - alpha/2)
    power = 1 - norm.cdf(z_alpha - z_observed)
    return power, z_observed, se_auc

def test_cohort_homogeneity(cohort1_data, cohort2_data):
    """测试两个队列的同质性"""
    # 基本特征比较
    hcc_ratio1 = cohort1_data['HCC'] / (cohort1_data['HCC'] + cohort1_data['FNH'])
    hcc_ratio2 = cohort2_data['HCC'] / (cohort2_data['HCC'] + cohort2_data['FNH'])
    
    # 卡方检验 - 疾病分布
    contingency_table = np.array([
        [cohort1_data['HCC'], cohort1_data['FNH']],
        [cohort2_data['HCC'], cohort2_data['FNH']]
    ])
    
    chi2, p_value, dof, expected = chi2_contingency(contingency_table)
    
    return {
        'hcc_ratio1': hcc_ratio1,
        'hcc_ratio2': hcc_ratio2,
        'chi2_stat': chi2,
        'p_value': p_value,
        'homogeneous': p_value > 0.05
    }

def analyze_pooling_scenarios():
    """分析不同合并策略的可行性"""
    
    print("=" * 80)
    print("内部验证与外部验证合并分析的可行性评估")
    print("=" * 80)
    
    # 研究数据
    internal_val = {"HCC": 16, "FNH": 9, "total": 25, "AUC": 0.965, "source": "医院A+B"}
    external_test = {"HCC": 20, "FNH": 8, "total": 28, "AUC": 0.851, "source": "医院C"}
    
    print("\n1. 队列基本信息:")
    print(f"   内部验证集: {internal_val['total']}例 (HCC:{internal_val['HCC']}, FNH:{internal_val['FNH']}) - {internal_val['source']}")
    print(f"   外部测试集: {external_test['total']}例 (HCC:{external_test['HCC']}, FNH:{external_test['FNH']}) - {external_test['source']}")
    print(f"   AUC差异: {internal_val['AUC'] - external_test['AUC']:.3f}")
    
    # 同质性检验
    homogeneity = test_cohort_homogeneity(internal_val, external_test)
    
    print(f"\n2. 队列同质性分析:")
    print(f"   内部验证集HCC比例: {homogeneity['hcc_ratio1']:.3f} ({homogeneity['hcc_ratio1']*100:.1f}%)")
    print(f"   外部测试集HCC比例: {homogeneity['hcc_ratio2']:.3f} ({homogeneity['hcc_ratio2']*100:.1f}%)")
    print(f"   卡方检验统计量: {homogeneity['chi2_stat']:.3f}")
    print(f"   P值: {homogeneity['p_value']:.3f}")
    print(f"   队列同质性: {'是' if homogeneity['homogeneous'] else '否'}")
    
    print(f"\n" + "="*80)
    print("方案1: 分别分析 (✅ 推荐)")
    print("="*80)
    
    # 分别分析
    power_internal, z_internal, se_internal = roc_power_analysis(
        internal_val['AUC'], internal_val['HCC'], internal_val['FNH']
    )
    
    power_external, z_external, se_external = roc_power_analysis(
        external_test['AUC'], external_test['HCC'], external_test['FNH']
    )
    
    print(f"内部验证集单独分析:")
    print(f"  AUC: {internal_val['AUC']}")
    print(f"  统计功效: {power_internal:.3f} ({power_internal*100:.1f}%)")
    print(f"  95% CI: 可计算精确置信区间")
    
    print(f"\n外部测试集单独分析:")
    print(f"  AUC: {external_test['AUC']}")
    print(f"  统计功效: {power_external:.3f} ({power_external*100:.1f}%)")
    print(f"  95% CI: 可计算精确置信区间")
    
    print(f"\n✅ 优势:")
    print(f"  • 保持内部/外部验证的概念清晰")
    print(f"  • 可以评估模型的泛化能力")
    print(f"  • 符合多中心研究的标准报告")
    print(f"  • 审稿人容易理解和接受")
    
    print(f"\n" + "="*80)
    print("方案2: 合并分析 (⚠️ 有条件可行)")
    print("="*80)
    
    # 合并分析
    pooled_hcc = internal_val['HCC'] + external_test['HCC']
    pooled_fnh = internal_val['FNH'] + external_test['FNH']
    pooled_total = pooled_hcc + pooled_fnh
    
    # 加权平均AUC (简化计算)
    weight_internal = internal_val['total'] / (internal_val['total'] + external_test['total'])
    weight_external = external_test['total'] / (internal_val['total'] + external_test['total'])
    pooled_auc = internal_val['AUC'] * weight_internal + external_test['AUC'] * weight_external
    
    power_pooled, z_pooled, se_pooled = roc_power_analysis(
        pooled_auc, pooled_hcc, pooled_fnh
    )
    
    print(f"合并队列分析:")
    print(f"  总样本量: {pooled_total}例 (HCC:{pooled_hcc}, FNH:{pooled_fnh})")
    print(f"  加权平均AUC: {pooled_auc:.3f}")
    print(f"  统计功效: {power_pooled:.3f} ({power_pooled*100:.1f}%)")
    print(f"  标准误: {se_pooled:.4f}")
    
    print(f"\n⚠️ 合并分析的条件:")
    if homogeneity['homogeneous']:
        print(f"  ✅ 队列同质性: 通过 (p={homogeneity['p_value']:.3f} > 0.05)")
    else:
        print(f"  ❌ 队列同质性: 不通过 (p={homogeneity['p_value']:.3f} < 0.05)")
    
    auc_diff = abs(internal_val['AUC'] - external_test['AUC'])
    if auc_diff < 0.1:
        print(f"  ✅ AUC差异可接受: {auc_diff:.3f} < 0.1")
    else:
        print(f"  ❌ AUC差异过大: {auc_diff:.3f} ≥ 0.1")
    
    print(f"\n⚠️ 合并分析的风险:")
    print(f"  • 掩盖了内部/外部验证的性能差异")
    print(f"  • 可能违反多中心研究的报告标准")
    print(f"  • 审稿人可能质疑方法学合理性")
    
    print(f"\n" + "="*80)
    print("方案3: Meta分析方法 (🔬 高级方法)")
    print("="*80)
    
    # 简化的meta分析
    # 计算各队列的方差
    var_internal = se_internal ** 2
    var_external = se_external ** 2
    
    # 固定效应模型
    weight_meta_internal = 1 / var_internal
    weight_meta_external = 1 / var_external
    total_weight = weight_meta_internal + weight_meta_external
    
    meta_auc = (internal_val['AUC'] * weight_meta_internal + external_test['AUC'] * weight_meta_external) / total_weight
    meta_se = sqrt(1 / total_weight)
    meta_z = (meta_auc - 0.5) / meta_se
    meta_power = 1 - norm.cdf(norm.ppf(0.975) - meta_z)
    
    print(f"Meta分析结果:")
    print(f"  合并AUC估计: {meta_auc:.3f}")
    print(f"  合并标准误: {meta_se:.4f}")
    print(f"  统计功效: {meta_power:.3f} ({meta_power*100:.1f}%)")
    print(f"  内部验证权重: {weight_meta_internal/total_weight:.3f}")
    print(f"  外部测试权重: {weight_meta_external/total_weight:.3f}")
    
    print(f"\n🔬 Meta分析的优势:")
    print(f"  • 科学严谨的合并方法")
    print(f"  • 考虑了各研究的权重")
    print(f"  • 可以评估异质性")
    print(f"  • 提供更精确的总体估计")
    
    print(f"\n" + "="*80)
    print("推荐策略")
    print("="*80)
    
    # 基于分析结果给出推荐
    if homogeneity['homogeneous'] and auc_diff < 0.1:
        recommendation = "可以考虑合并分析，但仍推荐分别报告"
        risk_level = "低风险"
    elif homogeneity['homogeneous'] or auc_diff < 0.15:
        recommendation = "谨慎合并，建议分别分析为主"
        risk_level = "中等风险"
    else:
        recommendation = "不建议合并，必须分别分析"
        risk_level = "高风险"
    
    print(f"基于您的数据特征:")
    print(f"  队列同质性: {'通过' if homogeneity['homogeneous'] else '不通过'}")
    print(f"  AUC差异: {auc_diff:.3f}")
    print(f"  合并风险: {risk_level}")
    print(f"  推荐策略: {recommendation}")
    
    print(f"\n最终建议:")
    print(f"1. 主要分析: 分别报告内部验证和外部测试的Power Analysis")
    print(f"2. 补充分析: 可以提供合并分析作为敏感性分析")
    print(f"3. 方法学说明: 在文中解释为什么选择分别分析")
    
    # 审稿人回复模板
    print(f"\n" + "="*80)
    print("审稿人回复模板")
    print("="*80)
    
    response = f"""
POWER ANALYSIS APPROACH:
We conducted power analysis separately for internal validation and external 
test cohorts to maintain the conceptual distinction between these validation 
approaches and to properly assess model generalizability.

INDIVIDUAL COHORT ANALYSIS:
- Internal validation (n={internal_val['total']}): Power = {power_internal:.3f} ({power_internal*100:.1f}%)
- External test (n={external_test['total']}): Power = {power_external:.3f} ({power_external*100:.1f}%)

RATIONALE FOR SEPARATE ANALYSIS:
1. Preserves the internal/external validation distinction
2. Allows assessment of model generalizability across institutions
3. Follows established reporting standards for multi-center studies
4. Provides transparent evaluation of performance consistency

SUPPLEMENTARY POOLED ANALYSIS:
When cohorts were pooled (n={pooled_total}), the combined power was {power_pooled:.3f} 
({power_pooled*100:.1f}%), supporting the adequacy of our overall sample size.

This approach provides both individual cohort assessment and overall 
sample size justification while maintaining methodological rigor.
    """
    
    print(response)

if __name__ == "__main__":
    analyze_pooling_scenarios()
